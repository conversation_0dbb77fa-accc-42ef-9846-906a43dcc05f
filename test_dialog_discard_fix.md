# Fix for JSON Data Loss on Dialog Discard

## Problem Description
When users click "Discard" in the edit dialog form, all JSON data from custom table widgets (material_detail, specification_detail, other_cost, etc.) disappears from the main view form.

## Root Cause
The issue was in the `onWillUpdateProps` hooks of the custom table widgets. When the dialog is discarded, <PERSON><PERSON><PERSON> reverts the record data, but the widgets were incorrectly interpreting this as empty data and applying default values instead of preserving the original data.

## Solution Applied
Modified the `onWillUpdateProps` logic in all custom table widgets to:

1. **Better distinguish between truly empty data and data reversion**
2. **Only apply default values when data is actually null/undefined/false**
3. **Preserve existing data when dialog is discarded**

## Files Modified

### 1. Material Detail Table Widget
- **File**: `local/garment_base/static/src/components/material_detail_table/table.js`
- **Lines**: 144-167
- **Change**: Enhanced data validation in `onWillUpdateProps`

### 2. Specification Detail Table Widget
- **File**: `local/garment_base/static/src/components/specification_detail_table/table.js`
- **Lines**: 133-154
- **Change**: Enhanced data validation in `onWillUpdateProps`

### 3. Other Cost Table Widget
- **File**: `local/garment_base/static/src/components/other_cost_table/table.js`
- **Lines**: 157-170
- **Change**: Enhanced data validation in `onWillUpdateProps`

### 4. Process Table Widget
- **File**: `local/garment_base/static/src/components/process_table/table.js`
- **Lines**: 127-140
- **Change**: Enhanced data validation in `onWillUpdateProps`

### 5. Finished Product Size Table Widget
- **File**: `local/garment_base/static/src/components/finished_product_size_table/table.js`
- **Lines**: 125-142
- **Change**: Enhanced data validation in `onWillUpdateProps`

### 6. Quantity List Table Widget
- **File**: `local/production_management/static/src/components/quantity_list_table/table.js`
- **Lines**: 125-142
- **Change**: Enhanced data validation in `onWillUpdateProps`

## Testing Steps

1. **Open an existing order/sample with JSON data**
2. **Click the "Edit" button to open the edit dialog**
3. **Make some changes to the data**
4. **Click "Discard" instead of "Save"**
5. **Verify that the original JSON data is still visible in the main view**

## Expected Behavior After Fix

- ✅ JSON data should remain visible after clicking "Discard"
- ✅ Only truly empty/new records should get default values
- ✅ Data should be properly preserved during dialog operations
- ✅ No performance impact on normal operations

## Technical Details

The fix works by checking the raw value more carefully:

```javascript
// Before (problematic)
const newArray = Array.isArray(nextProps.record.data[this.props.name])
    ? nextProps.record.data[this.props.name]
    : defaultValues;

// After (fixed)
const rawValue = nextProps.record.data[this.props.name];
if (Array.isArray(rawValue) && rawValue.length > 0) {
    newArray = rawValue;
} else if (rawValue === null || rawValue === undefined || rawValue === false) {
    newArray = defaultValues;
} else {
    return; // Keep existing data
}
```

This ensures that only truly empty fields get default values, while data reversion from dialog discard preserves the original data.
