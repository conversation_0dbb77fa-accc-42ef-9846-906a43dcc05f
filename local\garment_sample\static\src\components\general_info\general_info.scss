.stats-container {
  background-color: #714B67;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  opacity: 0;
  transform: translateY(-20px);
  animation: wipeDown 0.5s ease-out forwards;
}

.stats-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 250px;
  text-align: center;
  opacity: 0;
  transform: translateY(-20px);
  animation: wipeDown 0.5s ease-out forwards;
  
  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stats-title {
  font-weight: 600;
  color: #6c757d;
  font-size: 1rem;
  margin-bottom: 10px;
}

.stats-value {
  font-size: 1.5rem;
}

@keyframes wipeDown {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
