/* filepath: c:\Users\<USER>\OneDrive\Documents\odoo-final-project\local\garment_production\static\src\scss\production_management.scss */

// Enhanced Production Management Styles

// Color Palette
$primary-color: #2E86C1;
$secondary-color: #85C1E9;
$success-color: #27AE60;
$warning-color: #F39C12;
$danger-color: #E74C3C;
$info-color: #17A2B8;
$light-gray: #F8F9FA;
$dark-gray: #495057;

// Hero Section
.o_production_hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);

    .oe_title h1 {
        color: white;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .oe_title h3 {
        color: rgba(255,255,255,0.8);
        font-weight: 400;
    }
}

// Stats Cards
.o_production_stats {
    display: flex;
    gap: 1rem;

    .stat-card {
        background: rgba(255,255,255,0.2);
        padding: 1.5rem;
        border-radius: 8px;
        text-align: center;
        backdrop-filter: blur(10px);
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    }
}

// Progress Steps
.o_production_steps {
    margin-bottom: 2rem;
    
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        padding: 0 2rem;
        
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 2rem;
            right: 2rem;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: white;
            padding: 0.5rem;
            border-radius: 50%;
            z-index: 2;
            transition: all 0.3s ease;
            
            .step-number {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-bottom: 0.5rem;
                background: #e9ecef;
                color: #6c757d;
                transition: all 0.3s ease;
            }
            
            .step-label {
                font-size: 0.8rem;
                text-align: center;
                color: #6c757d;
                white-space: nowrap;
            }
            
            &.active {
                .step-number {
                    background: $primary-color;
                    color: white;
                    box-shadow: 0 0 0 4px rgba(46, 134, 193, 0.3);
                }
                .step-label {
                    color: $primary-color;
                    font-weight: 600;
                }
            }
            
            &.completed {
                .step-number {
                    background: $success-color;
                    color: white;
                    
                    &::before {
                        content: '✓';
                    }
                }
                .step-label {
                    color: $success-color;
                }
            }
        }
    }
}

// Enhanced Cards
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .card-header {
        background: linear-gradient(45deg, $light-gray, white);
        border-bottom: 2px solid #e9ecef;
        border-radius: 12px 12px 0 0 !important;
        padding: 1.25rem;
        
        h5 {
            margin: 0;
            color: $dark-gray;
            font-weight: 600;
            
            i {
                margin-right: 0.5rem;
                color: $primary-color;
            }
        }
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

// Status Pills
.o_status_pills {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
    
    .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        
        i {
            margin-right: 0.3rem;
        }
    }
}

// Enhanced Alerts
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem;
    
    &.alert-info {
        background: linear-gradient(45deg, #e3f2fd, #bbdefb);
        color: #0d47a1;
        border-left: 4px solid $info-color;
    }
    
    &.alert-warning {
        background: linear-gradient(45deg, #fff3e0, #ffcc80);
        color: #e65100;
        border-left: 4px solid $warning-color;
    }
    
    &.alert-success {
        background: linear-gradient(45deg, #e8f5e8, #c8e6c9);
        color: #1b5e20;
        border-left: 4px solid $success-color;
    }
    
    i {
        font-size: 1.2rem;
    }
}

// Enhanced Tabs
.o_production_notebook {
    margin-top: 2rem;
    
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        
        .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            padding: 1rem 1.5rem;
            color: $dark-gray;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
                background: $light-gray;
                color: $primary-color;
            }
            
            &.active {
                background: $primary-color;
                color: white;
                border-bottom: 2px solid $primary-color;
            }
        }
    }
    
    .tab-content {
        background: white;
        border-radius: 0 8px 8px 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .tab-content-header {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
        
        h6 {
            margin: 0;
            color: $dark-gray;
            font-weight: 600;
            
            i {
                margin-right: 0.5rem;
                color: $primary-color;
            }
        }
        
        p {
            margin: 0.5rem 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }
    }
}

// Enhanced Tables
.o_list_view {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    
    th {
        background: linear-gradient(45deg, $light-gray, white);
        color: $dark-gray;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
    }
    
    td {
        border-bottom: 1px solid #f8f9fa;
    }
    
    tr:hover {
        background: linear-gradient(45deg, #f8f9fa, white);
    }
}

// Cost Summary
.cost-summary {
    background: $light-gray;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.o_production_footer {
    margin-top: 2rem;
    padding: 1.5rem;
    background: $light-gray;
    border-radius: 8px;
    
    .finance-status {
        display: flex;
        align-items: center;
        gap: 1rem;
        
        label {
            font-weight: 600;
            color: $dark-gray;
        }
    }
    
    .total-cost-card {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        
        .cost-breakdown {
            .cost-line {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem 0;
                
                &.total {
                    font-size: 1.1rem;
                    
                    span, .o_field_widget {
                        font-weight: bold;
                        color: $primary-color;
                    }
                }
            }
        }
    }
}

// Form Field Enhancements
.o_form_view {
    .o_field_widget {
        
        &.o_field_monetary,
        &.o_field_float,
        &.o_field_integer {
            input {
                text-align: right;
                font-weight: 600;
            }
        }
    }
}

// Quantity List Table Enhancements
.size_quantity_table_container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 1rem 0;
    
    table {
        border-radius: 8px;
        overflow: hidden;
        
        th {
            background: linear-gradient(45deg, $primary-color, #5499c7);
            color: white;
            font-weight: 600;
            padding: 1rem;
            text-align: center;
        }
        
        td {
            padding: 0.75rem;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .form-control {
            border-radius: 6px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            
            &:focus {
                border-color: $primary-color;
                box-shadow: 0 0 0 3px rgba(46, 134, 193, 0.1);
            }
        }
        
        .btn-remove-row {
            border-radius: 6px;
            transition: all 0.3s ease;
            
            &:hover {
                transform: scale(1.05);
            }
        }
    }
    
    .btn-add-row {
        background: linear-gradient(45deg, $success-color, #58d68d);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .o_production_hero {
        padding: 1rem;
        
        .o_production_stats {
            flex-direction: column;
            margin-top: 1rem;
        }
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
        
        &::before {
            display: none;
        }
        
        .step {
            width: 100%;
            flex-direction: row;
            justify-content: flex-start;
            gap: 1rem;
            
            .step-number {
                margin-bottom: 0;
            }
        }
    }
    
    .o_production_footer {
        .row {
            flex-direction: column;
        }
        
        .finance-status {
            margin-bottom: 1rem;
        }
    }
}

// Animation for loading states
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
    .card {
        background: #2c3e50;
        color: white;
        
        .card-header {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
        }
    }
    
    .o_production_hero {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
}
.sample-float-hints {
  position: absolute;
  top: 180px; // or tweak depending on where sample info starts
  right: 40px;
  width: 320px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none; // if you want clicks to pass through
}


.sample-float-hints .alert {
  min-height: 64px; // đảm bảo alert nào cũng đủ cao để không nhìn quá ngắn
}

.table-responsive {
  overflow-x: auto;
  width: 100%;
  max-width: 100%;
}
.o_list_view thead th {
  white-space: nowrap;
}

.o_order_lines_table {
    width: 100% !important;
    table-layout: fixed !important;
    
    th, td {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 8px 4px !important;
        overflow: visible !important;
        white-space: nowrap !important;
    }
    
    // Size column
    th:nth-child(1), td:nth-child(1) {
        width: 80px !important;
        min-width: 80px !important;
        max-width: 80px !important;
    }
    
    // Color column  
    th:nth-child(2), td:nth-child(2) {
        width: 120px !important;
        min-width: 120px !important;
        max-width: 120px !important;
    }
    
    // Planned Qty column
    th:nth-child(3), td:nth-child(3) {
        width: 100px !important;
        min-width: 100px !important;
        max-width: 100px !important;
    }
    
    // Done Qty column
    th:nth-child(4), td:nth-child(4) {
        width: 100px !important;
        min-width: 100px !important;
        max-width: 100px !important;
    }
    
    // Input field styling
    input[type="text"], 
    input[type="number"],
    .o_field_widget input {
        width: 100% !important;
        padding: 4px 6px !important;
        font-size: 0.9rem !important;
        text-align: center !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
    }
    
    // Header text
    th {
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        background: #f8f9fa !important;
        border-bottom: 2px solid #dee2e6 !important;
    }
}

// Table container fixes
.table-responsive {
    overflow-x: auto !important;
    width: 100% !important;
    max-width: 100% !important;
    
    table {
        width: 100% !important;
        margin-bottom: 0 !important;
    }
}

// Fix for list view tables in general
.o_list_view {
    table {
        table-layout: fixed !important;
        width: 100% !important;
    }
    
    th {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        position: relative !important;
    }
    
    td {
        overflow: visible !important;
        white-space: nowrap !important;
    }
}

// Bundles Simple Table
.o_bundles_simple_table {
    width: 100% !important;
    table-layout: fixed !important;
    
    th, td {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 8px 6px !important;
        font-size: 0.9rem !important;
        border: 1px solid #e9ecef !important;
    }
    
    th {
        background: #f8f9fa !important;
        font-weight: 600 !important;
        color: #495057 !important;
    }
    
    // Column widths
    th:nth-child(1), td:nth-child(1) { width: 80px !important; } // Size
    th:nth-child(2), td:nth-child(2) { width: 120px !important; } // Color  
    th:nth-child(3), td:nth-child(3) { width: 100px !important; } // Planned
    th:nth-child(4), td:nth-child(4) { width: 100px !important; } // Done
    
    input[type="text"], 
    input[type="number"] {
        width: 100% !important;
        padding: 4px 6px !important;
        text-align: center !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
    }
}

