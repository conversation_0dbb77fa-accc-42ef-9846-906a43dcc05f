version: "3.9"
services:
  db:
    image: postgres:16
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
    volumes:
      - db-data:/var/lib/postgresql/data

  odoo:
    image: dfa8336hgjz/hust-garment-odoo:1.0
    depends_on:
      - db
    ports:
      - "8069:8069"
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    volumes:
      - ./filestore:/var/lib/odoo/filestore
      - ./local:/opt/odoo/local
      - ./odoo.cfg:/etc/odoo/odoo.conf

volumes:
  db-data:
  filestore:
