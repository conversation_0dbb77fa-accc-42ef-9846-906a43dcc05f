<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="garment.ProcessTable">
        <div class="d-flex justify-content-between mb-2" t-if="!props.readonly">
            <!-- Left group -->
            <div class="d-flex">
                <button type="button" class="btn btn-primary btn-sm" t-on-click="addRow">
                    Add Process
                </button>
            </div>
            <!-- Right group -->
            <div class="d-flex">
                <button type="button" class="btn btn-primary btn-sm me-1" t-on-click="() => saveTemplate()">
                    Save Template
                </button>
                <button type="button" class="btn btn-primary btn-sm" t-on-click="() => useTemplate()">
                    Use Template
                </button>
            </div>
        </div>

        <t t-if="!state.rows || state.rows.length === 0">
            <div class="alert alert-info text-center m-3">
                <i class="fa fa-info-circle me-2"/>
                No process data available. Let's start creating a process.
            </div>
        </t>
        <t t-if="state.rows and state.rows.length">
            <table class="table table-sm align-middle text-center">
                <thead>
                    <tr>
                        <th class="col-1">Serial Number</th>
                        <th>Name</th>
                        <th>Unit Price</th>
                        <th>Multiplier</th>
                        <th>Notes</th>
                        <th t-if="!props.readonly" class="col-2">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <t t-foreach="state.rows" t-as="row" t-key="row.serial_number">
                        <tr>
                            <td class="col-1"><input type="text" class="form-control" t-att-value="row.serial_number" readonly="readonly"/></td>
                            <td><input type="text" class="form-control" t-att-value="row.name" t-on-input="(event) => updateCell(event, 'name', row.serial_number)" t-att-disabled="props.readonly"/></td>
                            <td><input type="number" class="form-control" t-att-value="row.unit_price" t-on-input="(event) => updateCell(event, 'unit_price', row.serial_number)" t-att-disabled="props.readonly"/></td>
                            <td><input type="number" class="form-control" t-att-value="row.multiplier" t-on-input="(event) => updateCell(event, 'multiplier', row.serial_number)" t-att-disabled="props.readonly"/></td>
                            <td><input type="text" class="form-control" t-att-value="row.note" t-on-input="(event) => updateCell(event, 'note', row.serial_number)" t-att-disabled="props.readonly"/></td>
                            <td t-if="!props.readonly" class="col-2 text-center">
                                <button type="button" class="btn btn-primary btn-sm me-1" t-on-click="() => moveUp(row.serial_number)"><i class="fa fa-arrow-up"/></button>
                                <button type="button" class="btn btn-primary btn-sm me-1" t-on-click="() => moveDown(row.serial_number)"><i class="fa fa-arrow-down"/></button>
                                <button type="button" class="btn btn-danger btn-sm" t-on-click="() => removeRow(row.serial_number)"><i class="fa fa-trash"/></button>
                            </td>
                        </tr>
                    </t>
                </tbody>
            </table>
        </t>
    </t>
</templates>
