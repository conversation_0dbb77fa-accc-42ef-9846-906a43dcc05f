<odoo>
    <record id="view_production_order_line_tree" model="ir.ui.view">
        <field name="name">production.order.line.tree</field>
        <field name="model">production.order.line</field>
        <field name="arch" type="xml">
            <tree string="Order Lines" decoration-info="progress == 0" decoration-warning="progress &gt; 0 and progress &lt; 100" decoration-success="progress &gt;= 100">
                <field name="order_id" string="Production Order"/>
                <field name="sample_name" string="Sample"/>
                <field name="client"/>
                <field name="size"/>
                <field name="color"/>
                <field name="planned_qty" string="Planned"/>
                <field name="done_qty" string="Done"/>
                <field name="progress" string="Progress %" widget="progressbar"/>
                <field name="completion_status" string="Status"/>
            </tree>
        </field>
    </record>

    <record id="view_production_order_line_form" model="ir.ui.view">
        <field name="name">production.order.line.form</field>
        <field name="model">production.order.line</field>
        <field name="arch" type="xml">
            <form string="Order Line">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="order_id" readonly="1"/>
                        </h1>
                        <h2>
                            Size: <field name="size" readonly="1" class="oe_inline"/>
                            - <field name="completion_status" readonly="1" class="oe_inline"/>
                        </h2>
                    </div>
                    
                    <group col="4">
                        <group string="Order Information" colspan="2">
                            <field name="sample_name" readonly="1"/>
                            <field name="client" readonly="1"/>
                            <field name="color"/>
                        </group>
                        <group string="Quantities" colspan="2">
                            <field name="planned_qty"/>
                            <field name="done_qty" readonly="1"/>
                            <field name="progress" widget="progressbar" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Bundles">
                            <div class="alert alert-info mb-3" role="alert">
                                <p><strong>Bundle Management:</strong> Check the "Completed" box when each bundle is finished. This will automatically update the progress.</p>
                            </div>
                            <field name="bundle_ids">
                                <tree editable="bottom" decoration-success="is_completed" decoration-muted="not is_completed">
                                    <field name="bundle_no" string="Bundle #"/>
                                    <field name="size" readonly="1"/>
                                    <field name="qty" string="Quantity"/>
                                    <field name="is_completed" string="Completed" widget="boolean_toggle"/>
                                    <field name="completion_date" string="Completed On" readonly="1"/>
                                    <field name="ticket_printed" string="Ticket Printed"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_production_order_line" model="ir.actions.act_window">
        <field name="name">Order Lines</field>
        <field name="res_model">production.order.line</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No order lines found
            </p>
            <p>
                Order lines are automatically created when you generate them from a sample in a production order.
            </p>
        </field>
    </record>

    <menuitem id="menu_production_order_line" name="Order Lines" 
              parent="menu_garment_production_root" action="action_production_order_line" sequence="20"/>
</odoo>