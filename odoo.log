2025-04-12 03:32:18,978 25504 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-04-12 03:32:18,978 25504 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-04-12 03:32:18,978 25504 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-04-12 03:32:18,978 25504 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-04-12 03:32:18,978 25504 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-04-12 03:32:18,979 25504 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-04-12 03:32:18,979 25504 INFO ? odoo: Odoo version 17.0+e-20231119 
2025-04-12 03:32:18,979 25504 INFO ? odoo: Using configuration file at C:\Users\<USER>\OneDrive\Documents\odoo-final-project\odoo.cfg 
2025-04-12 03:32:18,989 25504 INFO ? odoo: addons paths: ['C:\\Users\\<USER>\\OneDrive\\Documents\\odoo-final-project\\src\\odoo\\odoo\\addons', 'c:\\users\\<USER>\\appdata\\local\\odoo\\addons\\17.0', 'c:\\users\\<USER>\\onedrive\\documents\\odoo-final-project\\local', 'c:\\users\\<USER>\\onedrive\\documents\\odoo-final-project\\src\\odoo\\addons', 'c:\\users\\<USER>\\onedrive\\documents\\odoo-final-project\\src\\odoo\\odoo\\addons'] 
2025-04-12 03:32:18,989 25504 INFO ? odoo: database: odoo@localhost:5432 
2025-04-12 03:32:19,243 25504 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-04-12 03:32:19,686 25504 WARNING ? odoo.service.server: 'watchdog' module not installed. Code autoreload feature is disabled 
2025-04-12 03:32:19,686 25504 DEBUG ? odoo.service.server: Setting signal handlers 
2025-04-12 03:32:19,692 25504 INFO ? odoo.service.server: HTTP service (werkzeug) running on My-ZBook:8069 
2025-04-12 03:32:19,853 25504 DEBUG ? odoo.modules.registry: Multiprocess load registry signaling: [Registry: 13] [Cache default: 5] [Cache assets: 1] [Cache templates: 12] [Cache routing: 1] 
2025-04-12 03:32:19,861 25504 INFO thanh123 odoo.modules.loading: loading 1 modules... 
2025-04-12 03:32:19,861 25504 DEBUG thanh123 odoo.modules.loading: Loading module base (1/1) 
2025-04-12 03:32:19,865 25504 DEBUG thanh123 odoo.modules.loading: Module base loaded in 0.00s, 0 queries 
2025-04-12 03:32:19,865 25504 INFO thanh123 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-04-12 03:32:19,882 25504 INFO thanh123 odoo.modules.loading: updating modules list 
2025-04-12 03:32:19,886 25504 INFO thanh123 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-04-12 03:32:22,335 25504 WARNING thanh123 odoo.modules.module: Missing `license` key in manifest for 'minimal_excel_web', defaulting to LGPL-3 
2025-04-12 03:32:22,720 25504 WARNING thanh123 odoo.modules.module: Missing `license` key in manifest for 'academy', defaulting to LGPL-3 
2025-04-12 03:32:22,823 25504 INFO thanh123 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Minimal Excel Web'] to user __system__ #1 via n/a 
2025-04-12 03:32:22,823 25504 INFO thanh123 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Minimal Excel Web'] to user __system__ #1 via n/a 
2025-04-12 03:32:23,106 25504 DEBUG ? odoo.http: HTTP sessions stored in: c:\users\<USER>\appdata\local\odoo\sessions 
2025-04-12 03:32:24,128 25504 INFO thanh123 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user __system__ #1 via n/a 
2025-04-12 03:32:24,149 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Install'"  
2025-04-12 03:32:24,149 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Apply Schedule Upgrade'"  
2025-04-12 03:32:24,160 25504 DEBUG thanh123 odoo.modules.loading: Updating graph with 47 more modules 
2025-04-12 03:32:24,160 25504 INFO thanh123 odoo.modules.loading: loading 48 modules... 
2025-04-12 03:32:24,160 25504 DEBUG thanh123 odoo.modules.loading: Loading module social_media (2/48) 
2025-04-12 03:32:24,163 25504 DEBUG thanh123 odoo.modules.loading: Module social_media loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,163 25504 DEBUG thanh123 odoo.modules.loading: Loading module web (3/48) 
2025-04-12 03:32:24,164 25504 DEBUG thanh123 odoo.modules.loading: Module web loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,164 25504 DEBUG thanh123 odoo.modules.loading: Loading module auth_totp (4/48) 
2025-04-12 03:32:24,194 25504 DEBUG thanh123 odoo.modules.loading: Module auth_totp loaded in 0.03s, 0 queries 
2025-04-12 03:32:24,195 25504 DEBUG thanh123 odoo.modules.loading: Loading module base_import (5/48) 
2025-04-12 03:32:24,203 25504 DEBUG thanh123 odoo.modules.loading: Module base_import loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,203 25504 DEBUG thanh123 odoo.modules.loading: Loading module base_import_module (6/48) 
2025-04-12 03:32:24,212 25504 DEBUG thanh123 odoo.modules.loading: Module base_import_module loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,212 25504 DEBUG thanh123 odoo.modules.loading: Loading module base_setup (7/48) 
2025-04-12 03:32:24,219 25504 DEBUG thanh123 odoo.modules.loading: Module base_setup loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,219 25504 DEBUG thanh123 odoo.modules.loading: Loading module bus (8/48) 
2025-04-12 03:32:24,277 25504 DEBUG thanh123 odoo.modules.loading: Module bus loaded in 0.06s, 0 queries 
2025-04-12 03:32:24,277 25504 DEBUG thanh123 odoo.modules.loading: Loading module garment_sample (9/48) 
2025-04-12 03:32:24,284 25504 DEBUG thanh123 odoo.modules.loading: Module garment_sample loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,284 25504 DEBUG thanh123 odoo.modules.loading: Loading module http_routing (10/48) 
2025-04-12 03:32:24,291 25504 DEBUG thanh123 odoo.modules.loading: Module http_routing loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,292 25504 DEBUG thanh123 odoo.modules.loading: Loading module resource (11/48) 
2025-04-12 03:32:24,304 25504 DEBUG thanh123 odoo.modules.loading: Module resource loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,305 25504 DEBUG thanh123 odoo.modules.loading: Loading module utm (12/48) 
2025-04-12 03:32:24,315 25504 DEBUG thanh123 odoo.modules.loading: Module utm loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,315 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_cohort (13/48) 
2025-04-12 03:32:24,336 25504 DEBUG thanh123 odoo.modules.loading: Module web_cohort loaded in 0.02s, 0 queries 
2025-04-12 03:32:24,336 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_gantt (14/48) 
2025-04-12 03:32:24,341 25504 DEBUG thanh123 odoo.modules.loading: Module web_gantt loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,341 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_grid (15/48) 
2025-04-12 03:32:24,348 25504 DEBUG thanh123 odoo.modules.loading: Module web_grid loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,349 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_tour (16/48) 
2025-04-12 03:32:24,353 25504 DEBUG thanh123 odoo.modules.loading: Module web_tour loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,353 25504 DEBUG thanh123 odoo.modules.loading: Loading module google_recaptcha (17/48) 
2025-04-12 03:32:24,357 25504 DEBUG thanh123 odoo.modules.loading: Module google_recaptcha loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,358 25504 DEBUG thanh123 odoo.modules.loading: Loading module iap (18/48) 
2025-04-12 03:32:24,364 25504 DEBUG thanh123 odoo.modules.loading: Module iap loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,364 25504 DEBUG thanh123 odoo.modules.loading: Loading module mail (19/48) 
2025-04-12 03:32:24,527 25504 DEBUG thanh123 odoo.modules.loading: Module mail loaded in 0.16s, 0 queries 
2025-04-12 03:32:24,527 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_editor (20/48) 
2025-04-12 03:32:24,557 25504 DEBUG thanh123 odoo.modules.loading: Module web_editor loaded in 0.03s, 0 queries 
2025-04-12 03:32:24,557 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_enterprise (21/48) 
2025-04-12 03:32:24,563 25504 DEBUG thanh123 odoo.modules.loading: Module web_enterprise loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,563 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_map (22/48) 
2025-04-12 03:32:24,572 25504 DEBUG thanh123 odoo.modules.loading: Module web_map loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,573 25504 DEBUG thanh123 odoo.modules.loading: Loading module auth_signup (23/48) 
2025-04-12 03:32:24,581 25504 DEBUG thanh123 odoo.modules.loading: Module auth_signup loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,581 25504 DEBUG thanh123 odoo.modules.loading: Loading module auth_totp_mail (24/48) 
2025-04-12 03:32:24,587 25504 DEBUG thanh123 odoo.modules.loading: Module auth_totp_mail loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,587 25504 DEBUG thanh123 odoo.modules.loading: Loading module base_install_request (25/48) 
2025-04-12 03:32:24,592 25504 DEBUG thanh123 odoo.modules.loading: Module base_install_request loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,593 25504 DEBUG thanh123 odoo.modules.loading: Loading module google_gmail (26/48) 
2025-04-12 03:32:24,601 25504 DEBUG thanh123 odoo.modules.loading: Module google_gmail loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,601 25504 DEBUG thanh123 odoo.modules.loading: Loading module iap_mail (27/48) 
2025-04-12 03:32:24,603 25504 DEBUG thanh123 odoo.modules.loading: Module iap_mail loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,603 25504 DEBUG thanh123 odoo.modules.loading: Loading module mail_bot (28/48) 
2025-04-12 03:32:24,610 25504 DEBUG thanh123 odoo.modules.loading: Module mail_bot loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,610 25504 DEBUG thanh123 odoo.modules.loading: Loading module phone_validation (29/48) 
2025-04-12 03:32:24,625 25504 DEBUG thanh123 odoo.modules.loading: Module phone_validation loaded in 0.02s, 0 queries 
2025-04-12 03:32:24,626 25504 DEBUG thanh123 odoo.modules.loading: Loading module privacy_lookup (30/48) 
2025-04-12 03:32:24,633 25504 DEBUG thanh123 odoo.modules.loading: Module privacy_lookup loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,633 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_mobile (31/48) 
2025-04-12 03:32:24,637 25504 DEBUG thanh123 odoo.modules.loading: Module web_mobile loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,637 25504 DEBUG thanh123 odoo.modules.loading: Loading module web_unsplash (32/48) 
2025-04-12 03:32:24,648 25504 DEBUG thanh123 odoo.modules.loading: Module web_unsplash loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,648 25504 DEBUG thanh123 odoo.modules.loading: Loading module iap_extract (33/48) 
2025-04-12 03:32:24,652 25504 DEBUG thanh123 odoo.modules.loading: Module iap_extract loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,652 25504 DEBUG thanh123 odoo.modules.loading: Loading module mail_enterprise (34/48) 
2025-04-12 03:32:24,655 25504 DEBUG thanh123 odoo.modules.loading: Module mail_enterprise loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,655 25504 DEBUG thanh123 odoo.modules.loading: Loading module partner_autocomplete (35/48) 
2025-04-12 03:32:24,665 25504 DEBUG thanh123 odoo.modules.loading: Module partner_autocomplete loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,665 25504 DEBUG thanh123 odoo.modules.loading: Loading module portal (36/48) 
2025-04-12 03:32:24,684 25504 DEBUG thanh123 odoo.modules.loading: Module portal loaded in 0.02s, 0 queries 
2025-04-12 03:32:24,685 25504 DEBUG thanh123 odoo.modules.loading: Loading module sms (37/48) 
2025-04-12 03:32:24,705 25504 DEBUG thanh123 odoo.modules.loading: Module sms loaded in 0.02s, 0 queries 
2025-04-12 03:32:24,706 25504 DEBUG thanh123 odoo.modules.loading: Loading module snailmail (38/48) 
2025-04-12 03:32:24,729 25504 DEBUG thanh123 odoo.modules.loading: Module snailmail loaded in 0.02s, 0 queries 
2025-04-12 03:32:24,729 25504 DEBUG thanh123 odoo.modules.loading: Loading module auth_totp_portal (39/48) 
2025-04-12 03:32:24,733 25504 DEBUG thanh123 odoo.modules.loading: Module auth_totp_portal loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,733 25504 DEBUG thanh123 odoo.modules.loading: Loading module digest (40/48) 
2025-04-12 03:32:24,742 25504 DEBUG thanh123 odoo.modules.loading: Module digest loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,742 25504 DEBUG thanh123 odoo.modules.loading: Loading module mail_mobile (41/48) 
2025-04-12 03:32:24,752 25504 DEBUG thanh123 odoo.modules.loading: Module mail_mobile loaded in 0.01s, 0 queries 
2025-04-12 03:32:24,752 25504 DEBUG thanh123 odoo.modules.loading: Loading module digest_enterprise (42/48) 
2025-04-12 03:32:24,752 25504 DEBUG thanh123 odoo.modules.loading: Module digest_enterprise loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,752 25504 DEBUG thanh123 odoo.modules.loading: Loading module website (43/48) 
2025-04-12 03:32:24,803 25504 DEBUG thanh123 odoo.modules.loading: Module website loaded in 0.05s, 0 queries 
2025-04-12 03:32:24,804 25504 DEBUG thanh123 odoo.modules.loading: Loading module academy (44/48) 
2025-04-12 03:32:24,809 25504 WARNING thanh123 odoo.models: The model academy.teachers has no _description 
2025-04-12 03:32:24,809 25504 DEBUG thanh123 odoo.modules.loading: Module academy loaded in 0.00s, 0 queries 
2025-04-12 03:32:24,809 25504 INFO thanh123 odoo.modules.loading: Loading module minimal_excel_web (45/48) 
2025-04-12 03:32:24,829 25504 DEBUG thanh123 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-04-12 03:32:24,833 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-04-12 03:32:24,833 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-04-12 03:32:24,833 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-04-12 03:32:24,869 25504 DEBUG thanh123 odoo.models: Patching res.config.settings.report_footer with translate=True 
2025-04-12 03:32:24,872 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.report_header with translate=True 
2025-04-12 03:32:24,872 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.report_footer with translate=True 
2025-04-12 03:32:24,872 25504 DEBUG thanh123 odoo.models: Patching base.document.layout.company_details with translate=True 
2025-04-12 03:32:24,894 25504 INFO thanh123 odoo.modules.registry: module minimal_excel_web: creating or updating database tables 
2025-04-12 03:32:24,956 25504 INFO thanh123 odoo.modules.loading: loading minimal_excel_web/models/minimal_excel_web_table.xml 
2025-04-12 03:32:24,971 25504 INFO thanh123 odoo.modules.loading: loading minimal_excel_web/security/ir.model.access.csv 
2025-04-12 03:32:24,983 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'See all possible values'"  
2025-04-12 03:32:24,983 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'external id'"  
2025-04-12 03:32:24,983 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for ""Unknown value '%s' for boolean field '%%(field)s'""  
2025-04-12 03:32:24,984 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for ""Use '1' for yes and '0' for no""  
2025-04-12 03:32:24,986 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'See all possible values'"  
2025-04-12 03:32:24,986 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'external id'"  
2025-04-12 03:32:24,987 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for ""Unknown value '%s' for boolean field '%%(field)s'""  
2025-04-12 03:32:24,987 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for ""Use '1' for yes and '0' for no""  
2025-04-12 03:32:24,991 25504 DEBUG thanh123 odoo.modules.registry: Invalidating default model caches from call_cache_clearing_methods C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_model.py:2065 
2025-04-12 03:32:24,993 25504 INFO thanh123 odoo.modules.loading: loading minimal_excel_web/views/excel_page.xml 
2025-04-12 03:32:25,000 25504 DEBUG thanh123 odoo.modules.registry: Invalidating templates model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_ui_view.py:520 
2025-04-12 03:32:25,006 25504 DEBUG thanh123 odoo.modules.registry: Invalidating templates model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_ui_view.py:520 
2025-04-12 03:32:25,011 25504 INFO thanh123 odoo.modules.loading: loading minimal_excel_web/views/excel_menu.xml 
2025-04-12 03:32:25,016 25504 DEBUG thanh123 odoo.modules.registry: Invalidating default model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_actions.py:84 
2025-04-12 03:32:25,019 25504 DEBUG thanh123 odoo.modules.registry: Invalidating default model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_ui_menu.py:172 
2025-04-12 03:32:25,051 25504 INFO thanh123 odoo.modules.loading: Module minimal_excel_web loaded in 0.24s, 88 queries (+88 other) 
2025-04-12 03:32:25,051 25504 DEBUG thanh123 odoo.modules.loading: Loading module website_enterprise (46/48) 
2025-04-12 03:32:25,052 25504 DEBUG thanh123 odoo.modules.loading: Module website_enterprise loaded in 0.00s, 0 queries 
2025-04-12 03:32:25,052 25504 DEBUG thanh123 odoo.modules.loading: Loading module website_mail (47/48) 
2025-04-12 03:32:25,056 25504 DEBUG thanh123 odoo.modules.loading: Module website_mail loaded in 0.00s, 0 queries 
2025-04-12 03:32:25,056 25504 DEBUG thanh123 odoo.modules.loading: Loading module website_sms (48/48) 
2025-04-12 03:32:25,060 25504 DEBUG thanh123 odoo.modules.loading: Module website_sms loaded in 0.00s, 0 queries 
2025-04-12 03:32:25,060 25504 INFO thanh123 odoo.modules.loading: 48 modules loaded in 0.90s, 88 queries (+88 extra) 
2025-04-12 03:32:25,158 25504 DEBUG thanh123 odoo.models: column auth_signup_uninvited is in the table res_config_settings but not in the corresponding object res.config.settings 
2025-04-12 03:32:25,158 25504 DEBUG thanh123 odoo.models: column password is in the table res_users but not in the corresponding object res.users 
2025-04-12 03:32:25,158 25504 DEBUG thanh123 odoo.models: column totp_secret is in the table res_users but not in the corresponding object res.users 
2025-04-12 03:32:25,180 25504 DEBUG thanh123 odoo.models: column web is in the table ir_module_module but not in the corresponding object ir.module.module 
2025-04-12 03:32:25,194 25504 DEBUG thanh123 odoo.models: column index is in the table res_users_apikeys but not in the corresponding object res.users.apikeys 
2025-04-12 03:32:25,194 25504 DEBUG thanh123 odoo.models: column key is in the table res_users_apikeys but not in the corresponding object res.users.apikeys 
2025-04-12 03:32:25,198 25504 DEBUG thanh123 odoo.models: column index is in the table auth_totp_device but not in the corresponding object auth_totp.device 
2025-04-12 03:32:25,198 25504 DEBUG thanh123 odoo.models: column key is in the table auth_totp_device but not in the corresponding object auth_totp.device 
2025-04-12 03:32:25,213 25504 DEBUG thanh123 odoo.models: column signup_token is in the table res_partner but not in the corresponding object res.partner 
2025-04-12 03:32:25,245 25504 DEBUG thanh123 odoo.modules.registry: Invalidating templates model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_ui_view.py:520 
2025-04-12 03:32:25,255 25504 DEBUG thanh123 odoo.modules.registry: Invalidating templates model caches from write C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\addons\base\models\ir_ui_view.py:520 
2025-04-12 03:32:25,713 25504 INFO thanh123 odoo.modules.loading: Modules loaded. 
2025-04-12 03:32:25,718 25504 INFO thanh123 odoo.modules.registry: Registry loaded in 6.031s 
2025-04-12 03:32:25,723 25504 DEBUG thanh123 odoo.service.server: cron0 started! 
2025-04-12 03:32:25,723 25504 DEBUG thanh123 odoo.service.server: cron1 started! 
2025-04-12 03:32:25,884 25504 INFO thanh123 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-04-12 03:32:25,884 25504 DEBUG thanh123 odoo.addons.website.models.ir_http: _generate_routing_rules for website: 1 
2025-04-12 03:32:25,885 25504 INFO thanh123 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-04-12 03:32:25,885 25504 DEBUG thanh123 odoo.addons.website.models.ir_http: _generate_routing_rules for website: 1 
2025-04-12 03:32:26,016 25504 DEBUG thanh123 odoo.addons.http_routing.models.ir_http: '/excel/table' (lang: 'en') no lang in url and default website, continue 
2025-04-12 03:32:26,031 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:26] "GET /websocket HTTP/1.1" 101 - 12 0.050 2.531
2025-04-12 03:32:26,087 25504 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-04-12 03:32:26,209 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="title or 'Odoo'" in template 510. Replace by @t-out 
2025-04-12 03:32:26,211 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='request.csrf_token(None)' in template 510. Replace by @t-out 
2025-04-12 03:32:26,212 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='debug' in template 510. Replace by @t-out 
2025-04-12 03:32:26,235 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="link['text']" in template 510. Replace by @t-out 
2025-04-12 03:32:26,242 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='website.google_analytics_key' in template 510. Replace by @t-out 
2025-04-12 03:32:26,542 25504 DEBUG thanh123 odoo.modules.module: module web.public.widget: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:26,852 25504 DEBUG thanh123 odoo.http: Couldn't load Geoip Country file ([Errno 2] No such file or directory: b'c:\\usr\\share\\geoip\\geolite2-country.mmdb'). Fallbacks on Geoip City. 
2025-04-12 03:32:26,852 25504 DEBUG thanh123 odoo.http: Couldn't load Geoip City file at c:\usr\share\geoip\geolite2-city.mmdb. IP Resolver disabled. 
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\http.py", line 1093, in _country_record
    return root.geoip_country_db.country(self.ip)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\http.py", line 2095, in geoip_country_db
    return geoip2.database.Reader(config['geoip_country_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\.venv\Lib\site-packages\geoip2\database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\.venv\Lib\site-packages\maxminddb\__init__.py", line 80, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'c:\\usr\\share\\geoip\\geolite2-country.mmdb'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\src\odoo\odoo\http.py", line 2084, in geoip_city_db
    return geoip2.database.Reader(config['geoip_city_db'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\.venv\Lib\site-packages\geoip2\database.py", line 86, in __init__
    self._db_reader = maxminddb.open_database(fileish, mode)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Documents\odoo-final-project\.venv\Lib\site-packages\maxminddb\__init__.py", line 80, in open_database
    return cast(Reader, _extension.Reader(database, mode))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: b'c:\\usr\\share\\geoip\\geolite2-city.mmdb'
2025-04-12 03:32:27,150 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang[2].split('/').pop()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,151 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang[1].split('_').pop(0).upper()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,155 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[2].split('/').pop()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,155 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[1].split('_').pop(0).upper()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,203 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 437. Replace by @t-out 
2025-04-12 03:32:27,383 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="user_id.name[:23] + '...' if user_id.name and len(user_id.name) > 25 else user_id.name" in template 437. Replace by @t-out 
2025-04-12 03:32:27,406 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang[2].split('/').pop()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,407 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="active_lang[1].split('_').pop(0).upper()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,410 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[2].split('/').pop()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,411 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[1].split('_').pop(0).upper()" in template 436. Replace by @t-out 
2025-04-12 03:32:27,462 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[2].split('/').pop()" in template 578. Replace by @t-out 
2025-04-12 03:32:27,463 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="lg[1].split('_').pop(0).upper()" in template 578. Replace by @t-out 
2025-04-12 03:32:27,470 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:27] "GET /excel/table HTTP/1.1" 200 - 193 0.246 4.119
2025-04-12 03:32:27,518 25504 DEBUG ? odoo.modules.module: module CONTRIBUTING.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,518 25504 DEBUG ? odoo.modules.module: module COPYRIGHT: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,535 25504 DEBUG ? odoo.modules.module: module l10n_generic_coa: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,543 25504 DEBUG ? odoo.modules.module: module LICENSE: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,555 25504 DEBUG ? odoo.modules.module: module README.md: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,572 25504 DEBUG ? odoo.modules.module: module __init__.py: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,573 25504 DEBUG ? odoo.modules.module: module __pycache__: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-04-12 03:32:27,837 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:27] "GET /web/assets/1/debug/web.assets_frontend_minimal.js HTTP/1.1" 304 - 10 0.015 0.013
2025-04-12 03:32:27,921 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:27] "GET /web/assets/1/debug/web.assets_frontend.css HTTP/1.1" 304 - 21 0.010 0.406
2025-04-12 03:32:28,584 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:28] "GET /web/assets/1/debug/web.assets_frontend_lazy.js HTTP/1.1" 304 - 22 0.011 0.259
2025-04-12 03:32:28,721 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 03:32:28] "POST /excel/templates HTTP/1.1" 200 - 3 0.003 0.003
2025-04-12 03:33:25,835 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:33:26,897 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:34:25,854 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:34:27,922 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:35:25,866 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:35:28,941 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:36:25,880 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:36:29,964 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:37:25,885 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:37:30,979 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:38:25,892 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:38:31,995 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:39:25,897 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:39:33,022 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:40:25,902 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:40:34,035 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:41:25,910 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:41:35,051 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:42:25,925 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:42:36,066 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 03:43:25,932 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 03:43:37,083 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 04:06:16,186 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 04:06:16,192 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 04:06:18,102 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [12/Apr/2025 04:06:18] "GET /websocket HTTP/1.1" 101 - 1 0.003 0.035
2025-04-12 13:08:48,748 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-12 13:08:48,749 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-12 13:08:49,194 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 14 acquired 
2025-04-12 13:08:49,287 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 14, skip 
2025-04-12 13:08:49,288 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 5 acquired 
2025-04-12 13:08:49,639 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Digest Emails', 174) 
2025-04-12 13:08:49,639 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-04-12 13:08:49,641 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 
2025-04-12 13:08:49,641 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     1, 
2025-04-12 13:08:49,641 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     '*', 
2025-04-12 13:08:49,641 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     'Notification: Delete Notifications older than 6 Month', 
2025-04-12 13:08:49,641 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     128) 
2025-04-12 13:08:49,641 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-04-12 13:08:49,650 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Digest Emails` done. 
2025-04-12 13:08:49,650 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.011s (cron Digest Emails, server action 174 with uid 1) 
2025-04-12 13:08:49,653 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 14 updated and released 
2025-04-12 13:08:49,653 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 5, skip 
2025-04-12 13:08:49,653 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 8 acquired 
2025-04-12 13:08:49,658 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Notification: Delete Notifications older than 6 Month` done. 
2025-04-12 13:08:49,658 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.016s (cron Notification: Delete Notifications older than 6 Month, server action 128 with uid 1) 
2025-04-12 13:08:49,659 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Mail: send web push notification', 131) 
2025-04-12 13:08:49,659 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-04-12 13:08:49,660 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 5 updated and released 
2025-04-12 13:08:49,661 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 8, skip 
2025-04-12 13:08:49,662 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 9 acquired 
2025-04-12 13:08:49,664 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Mail: send web push notification` done. 
2025-04-12 13:08:49,664 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.006s (cron Mail: send web push notification, server action 131 with uid 1) 
2025-04-12 13:08:49,667 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 8 updated and released 
2025-04-12 13:08:49,668 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 9, skip 
2025-04-12 13:08:49,668 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 10 acquired 
2025-04-12 13:08:49,671 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Discuss: channel member unmute', 132) 
2025-04-12 13:08:49,671 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-04-12 13:08:49,679 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Discuss: channel member unmute` done. 
2025-04-12 13:08:49,679 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.008s (cron Discuss: channel member unmute, server action 132 with uid 1) 
2025-04-12 13:08:49,682 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 9 updated and released 
2025-04-12 13:08:49,682 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Users: Notify About Unregistered Users', 142) 
2025-04-12 13:08:49,683 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 10, skip 
2025-04-12 13:08:49,683 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-04-12 13:08:49,683 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 16 acquired 
2025-04-12 13:08:49,691 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Website Visitor : clean inactive visitors', 179) 
2025-04-12 13:08:49,691 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-04-12 13:08:49,699 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Website Visitor : clean inactive visitors` done. 
2025-04-12 13:08:49,700 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.009s (cron Website Visitor : clean inactive visitors, server action 179 with uid 1) 
2025-04-12 13:08:49,701 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 16 updated and released 
2025-04-12 13:08:49,702 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Users: Notify About Unregistered Users` done. 
2025-04-12 13:08:49,702 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.019s (cron Users: Notify About Unregistered Users, server action 142 with uid 1) 
2025-04-12 13:08:49,702 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 11 acquired 
2025-04-12 13:08:49,704 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 10 updated and released 
2025-04-12 13:08:49,705 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 16, skip 
2025-04-12 13:08:49,706 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 11, skip 
2025-04-12 13:08:49,707 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 12 acquired 
2025-04-12 13:08:49,709 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Partner Autocomplete: Sync with remote DB', 156) 
2025-04-12 13:08:49,710 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-04-12 13:08:49,713 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'SMS: SMS Queue Manager', 160) 
2025-04-12 13:08:49,714 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-04-12 13:08:49,716 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete: Sync with remote DB` done. 
2025-04-12 13:08:49,716 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.006s (cron Partner Autocomplete: Sync with remote DB, server action 156 with uid 1) 
2025-04-12 13:08:49,719 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 11 updated and released 
2025-04-12 13:08:49,719 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 12, skip 
2025-04-12 13:08:49,720 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 13 acquired 
2025-04-12 13:08:49,721 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-04-12 13:08:49,721 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.007s (cron SMS: SMS Queue Manager, server action 160 with uid 1) 
2025-04-12 13:08:49,724 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 12 updated and released 
2025-04-12 13:08:49,725 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Snailmail: process letters queue', 170) 
2025-04-12 13:08:49,725 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 13, skip 
2025-04-12 13:08:49,725 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-04-12 13:08:49,726 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 7 acquired 
2025-04-12 13:08:49,733 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Notification: Send scheduled message notifications', 130) 
2025-04-12 13:08:49,734 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-04-12 13:08:49,734 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-04-12 13:08:49,734 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.008s (cron Snailmail: process letters queue, server action 170 with uid 1) 
2025-04-12 13:08:49,739 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 13 updated and released 
2025-04-12 13:08:49,741 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 7, skip 
2025-04-12 13:08:49,742 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-04-12 13:08:49,742 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 3 acquired 
2025-04-12 13:08:49,742 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.009s (cron Notification: Send scheduled message notifications, server action 130 with uid 1) 
2025-04-12 13:08:49,748 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 7 updated and released 
2025-04-12 13:08:49,749 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Mail: Email Queue Manager', 126) 
2025-04-12 13:08:49,750 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-04-12 13:08:49,750 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 3, skip 
2025-04-12 13:08:49,760 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-04-12 13:08:49,760 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.010s (cron Mail: Email Queue Manager, server action 126 with uid 1) 
2025-04-12 13:08:49,761 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 3 updated and released 
2025-04-13 14:34:04,654 25504 INFO thanh123 werkzeug: 127.0.0.1 - - [13/Apr/2025 14:34:04] "GET /websocket HTTP/1.1" 101 - 1 0.004 0.091
2025-04-13 14:34:49,234 25504 DEBUG ? odoo.service.server: cron0 polling for jobs 
2025-04-13 14:34:49,242 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 13 acquired 
2025-04-13 14:34:49,452 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Snailmail: process letters queue', 170) 
2025-04-13 14:34:49,453 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-04-13 14:34:49,465 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-04-13 14:34:49,467 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.014s (cron Snailmail: process letters queue, server action 170 with uid 1) 
2025-04-13 14:34:49,470 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 13 updated and released 
2025-04-13 14:34:49,471 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 7 acquired 
2025-04-13 14:34:49,475 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Notification: Send scheduled message notifications', 130) 
2025-04-13 14:34:49,475 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-04-13 14:34:49,479 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-04-13 14:34:49,479 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.004s (cron Notification: Send scheduled message notifications, server action 130 with uid 1) 
2025-04-13 14:34:49,481 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 7 updated and released 
2025-04-13 14:34:49,481 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 3 acquired 
2025-04-13 14:34:49,486 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Mail: Email Queue Manager', 126) 
2025-04-13 14:34:49,486 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-04-13 14:34:49,493 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-04-13 14:34:49,493 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.007s (cron Mail: Email Queue Manager, server action 126 with uid 1) 
2025-04-13 14:34:49,494 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 3 updated and released 
2025-04-13 14:34:49,495 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 1 acquired 
2025-04-13 14:34:49,498 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Base: Auto-vacuum internal data', 34) 
2025-04-13 14:34:49,499 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-04-13 14:34:49,505 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling wizard.ir.model.menu.create()._transient_vacuum() 
2025-04-13 14:34:49,511 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling reset.view.arch.wizard()._transient_vacuum() 
2025-04-13 14:34:49,518 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.attachment()._gc_file_store() 
2025-04-13 14:34:49,556 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\02/0259f806154656f69da5072ec29c3a1114f5fba3 
2025-04-13 14:34:49,560 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\13/1395a38bb1767792c86f83547c1b580a1f276804 
2025-04-13 14:34:49,562 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\15/15aedc4d5baea62961364168cf7cb8ced0b9064b 
2025-04-13 14:34:49,564 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\16/1665d91330d9a7c36c4dcc182933e98f798b5398 
2025-04-13 14:34:49,566 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\1b/1b23910df543ba28c4b81b2c5077467d5dc282ba 
2025-04-13 14:34:49,568 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\1d/1dfb080aa1855cc55b19ccd9ada9f06aa41ce485 
2025-04-13 14:34:49,570 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\23/23898cf42afe020d303e73956263b70d6f9bad55 
2025-04-13 14:34:49,572 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\28/285b84a770b2e0737c7fe0795d654a827e7f34c0 
2025-04-13 14:34:49,576 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\2c/2cc7b1c75940ee4c9e0750b316c2e31ce810f351 
2025-04-13 14:34:49,578 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\3a/3a0ea6a71c807714982ba9d1a3b834bef0787842 
2025-04-13 14:34:49,581 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\53/537b8720084cdeecb5a9c391453cf5fc7f963ef7 
2025-04-13 14:34:49,582 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\60/605c7b4104e0075c8890de32dc153383a78d7f3c 
2025-04-13 14:34:49,584 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\60/607530b1d75b116fd2db1c939c66939031449698 
2025-04-13 14:34:49,585 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\66/6602426affa99996fcd25e946d319720e83dc337 
2025-04-13 14:34:49,588 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\83/83725d22dfbb0278b94c4dcfe77fc0a2c93f76b9 
2025-04-13 14:34:49,589 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\84/84f3998aef98e04f8255fa51f33b6184d74511af 
2025-04-13 14:34:49,591 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\89/89abf485394f5ed250e292f63f5ed3437eebf524 
2025-04-13 14:34:49,593 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\91/91d1dbe6d1cce7d023dbd246562fca0dfc74a42a 
2025-04-13 14:34:49,595 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\95/954a253a16b5055f33ae44cdbda3fc498b43f8ff 
2025-04-13 14:34:49,596 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\96/96de228b366d91718d1aaacbfc9af7c76d6dc644 
2025-04-13 14:34:49,599 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\a1/a1c916140dbd3b83fef65986cc93910dc79727c2 
2025-04-13 14:34:49,600 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\a3/a3579bb5fb8e1cc60166dc5dceacbad3bedb1c47 
2025-04-13 14:34:49,603 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\a7/a761b67c2759ca0dd0c7f5508035c9b1df2e8bda 
2025-04-13 14:34:49,605 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\af/afb8116afe1e206b62ea133016a9e94d42002621 
2025-04-13 14:34:49,607 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\b8/b8c11a48d0242af3110bcf615d6040690fe1afa7 
2025-04-13 14:34:49,609 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\bf/bf835fb3daafa0bea40352e4ee4f241ce37c22bf 
2025-04-13 14:34:49,611 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\bf/bfd24726b11d801fd96e578f1f091eba9c310fb6 
2025-04-13 14:34:49,613 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\c9/c90790097e0e5f7accc3311efc5fa32ec4ffd61a 
2025-04-13 14:34:49,615 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\d4/d4fc69920ad60760620386cff833b626f572c7ce 
2025-04-13 14:34:49,616 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\d7/d78a6d400b03dc17d7a51cf463eecad3b7de2c3d 
2025-04-13 14:34:49,618 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\e2/e247bed7410ce6facaa2be1453bd3aab11966407 
2025-04-13 14:34:49,622 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\e6/e67c0fc8eaef317de33e9c4c0076de9f72aa1a2e 
2025-04-13 14:34:49,624 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\eb/eb1795215f4d0238a49bb99fa785182d07028e74 
2025-04-13 14:34:49,626 25504 DEBUG thanh123 odoo.addons.base.models.ir_attachment: _file_gc unlinked c:\users\<USER>\appdata\local\odoo\filestore\thanh123\f8/f873bd960313f1efa80f8c7f48c3be35f2957d80 
2025-04-13 14:34:49,626 25504 INFO thanh123 odoo.addons.base.models.ir_attachment: filestore gc 38 checked, 34 removed 
2025-04-13 14:34:49,629 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.cron.trigger()._gc_cron_triggers() 
2025-04-13 14:34:49,649 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.http()._gc_sessions() 
2025-04-13 14:34:49,658 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.demo()._transient_vacuum() 
2025-04-13 14:34:49,661 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.demo_failure()._transient_vacuum() 
2025-04-13 14:34:49,664 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.demo_failure.wizard()._transient_vacuum() 
2025-04-13 14:34:49,667 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling ir.profile()._gc_profile() 
2025-04-13 14:34:49,670 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.enable.profiling.wizard()._transient_vacuum() 
2025-04-13 14:34:49,683 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.config()._transient_vacuum() 
2025-04-13 14:34:49,686 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.config.installer()._transient_vacuum() 
2025-04-13 14:34:49,690 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.config.settings()._transient_vacuum() 
2025-04-13 14:34:49,695 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.users.log()._gc_user_logs() 
2025-04-13 14:34:49,697 25504 INFO thanh123 odoo.addons.base.models.res_users: GC'd 2 user log entries 
2025-04-13 14:34:49,700 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.users.identitycheck()._transient_vacuum() 
2025-04-13 14:34:49,703 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling change.password.wizard()._transient_vacuum() 
2025-04-13 14:34:49,706 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling change.password.user()._transient_vacuum() 
2025-04-13 14:34:49,708 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling change.password.own()._transient_vacuum() 
2025-04-13 14:34:49,710 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling res.users.apikeys.description()._transient_vacuum() 
2025-04-13 14:34:49,715 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.module.update()._transient_vacuum() 
2025-04-13 14:34:49,717 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.language.install()._transient_vacuum() 
2025-04-13 14:34:49,719 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.language.import()._transient_vacuum() 
2025-04-13 14:34:49,722 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.module.upgrade()._transient_vacuum() 
2025-04-13 14:34:49,724 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.module.uninstall()._transient_vacuum() 
2025-04-13 14:34:49,726 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.language.export()._transient_vacuum() 
2025-04-13 14:34:49,728 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.partner.merge.line()._transient_vacuum() 
2025-04-13 14:34:49,730 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.partner.merge.automatic.wizard()._transient_vacuum() 
2025-04-13 14:34:49,732 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.document.layout()._transient_vacuum() 
2025-04-13 14:34:49,734 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling auth_totp.device()._gc_device() 
2025-04-13 14:34:49,735 25504 INFO thanh123 odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-04-13 14:34:49,737 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling auth_totp.wizard()._transient_vacuum() 
2025-04-13 14:34:49,740 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base_import.import()._transient_vacuum() 
2025-04-13 14:34:49,741 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.import.module()._transient_vacuum() 
2025-04-13 14:34:49,743 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling bus.bus()._gc_messages() 
2025-04-13 14:34:49,762 25504 INFO thanh123 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131] 
2025-04-13 14:34:49,763 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling bus.presence()._gc_bus_presence() 
2025-04-13 14:34:49,777 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling iap.account.info()._transient_vacuum() 
2025-04-13 14:34:49,791 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.activity()._gc_delete_old_overdue_activities() 
2025-04-13 14:34:49,800 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.message.translation()._gc_translations() 
2025-04-13 14:34:49,812 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling discuss.channel.rtc.session()._gc_inactive_sessions() 
2025-04-13 14:34:49,817 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.blacklist.remove()._transient_vacuum() 
2025-04-13 14:34:49,821 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.compose.message()._gc_lost_attachments() 
2025-04-13 14:34:49,823 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.compose.message()._transient_vacuum() 
2025-04-13 14:34:49,825 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.activity.schedule()._transient_vacuum() 
2025-04-13 14:34:49,828 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.resend.message()._transient_vacuum() 
2025-04-13 14:34:49,830 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.resend.partner()._transient_vacuum() 
2025-04-13 14:34:49,831 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.template.preview()._transient_vacuum() 
2025-04-13 14:34:49,833 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.template.reset()._transient_vacuum() 
2025-04-13 14:34:49,835 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling mail.wizard.invite()._transient_vacuum() 
2025-04-13 14:34:49,837 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.module.install.request()._transient_vacuum() 
2025-04-13 14:34:49,840 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling base.module.install.review()._transient_vacuum() 
2025-04-13 14:34:49,843 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling phone.blacklist.remove()._transient_vacuum() 
2025-04-13 14:34:49,845 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling privacy.lookup.wizard()._transient_vacuum() 
2025-04-13 14:34:49,848 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling privacy.lookup.wizard.line()._transient_vacuum() 
2025-04-13 14:34:49,852 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling portal.share()._transient_vacuum() 
2025-04-13 14:34:49,855 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling portal.wizard()._transient_vacuum() 
2025-04-13 14:34:49,857 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling portal.wizard.user()._transient_vacuum() 
2025-04-13 14:34:49,858 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.sms()._gc_device() 
2025-04-13 14:34:49,859 25504 INFO thanh123 odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-04-13 14:34:49,861 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.composer()._transient_vacuum() 
2025-04-13 14:34:49,864 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.resend.recipient()._transient_vacuum() 
2025-04-13 14:34:49,866 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.resend()._transient_vacuum() 
2025-04-13 14:34:49,868 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.template.preview()._transient_vacuum() 
2025-04-13 14:34:49,869 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling sms.template.reset()._transient_vacuum() 
2025-04-13 14:34:49,872 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling snailmail.letter.format.error()._transient_vacuum() 
2025-04-13 14:34:49,874 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling snailmail.letter.missing.required.fields()._transient_vacuum() 
2025-04-13 14:34:49,883 25504 DEBUG thanh123 odoo.addons.base.models.ir_autovacuum: Calling website.robots()._transient_vacuum() 
2025-04-13 14:34:49,887 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Base: Auto-vacuum internal data` done. 
2025-04-13 14:34:49,887 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.387s (cron Base: Auto-vacuum internal data, server action 34 with uid 1) 
2025-04-13 14:34:49,888 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 1 updated and released 
2025-04-13 14:34:49,888 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 2 acquired 
2025-04-13 14:34:49,892 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Base: Portal Users Deletion', 35) 
2025-04-13 14:34:49,892 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Base: Portal Users Deletion`. 
2025-04-13 14:34:49,897 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Base: Portal Users Deletion` done. 
2025-04-13 14:34:49,897 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.005s (cron Base: Portal Users Deletion, server action 35 with uid 1) 
2025-04-13 14:34:49,898 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 2 updated and released 
2025-04-13 14:34:49,899 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 14 acquired 
2025-04-13 14:34:49,903 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Digest Emails', 174) 
2025-04-13 14:34:49,903 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-04-13 14:34:49,916 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Connect'"  
2025-04-13 14:34:49,930 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Last 24 hours'"  
2025-04-13 14:34:49,930 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Last 7 Days'"  
2025-04-13 14:34:49,930 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Last 30 Days'"  
2025-04-13 14:34:50,064 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Prefer a broader overview?'"  
2025-04-13 14:34:50,064 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Switch to weekly Digests'"  
2025-04-13 14:34:50,064 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Want to customize this email?'"  
2025-04-13 14:34:50,064 25504 DEBUG thanh123 odoo.tools.translate: no translation language detected, skipping translation for "'Choose the metrics you care about'"  
2025-04-13 14:34:50,102 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='top_button_label' in template 488. Replace by @t-out 
2025-04-13 14:34:50,102 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='title' in template 488. Replace by @t-out 
2025-04-13 14:34:50,103 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='sub_title' in template 488. Replace by @t-out 
2025-04-13 14:34:50,103 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='formatted_date' in template 488. Replace by @t-out 
2025-04-13 14:34:50,105 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="kpi_info['kpi_fullname']" in template 488. Replace by @t-out 
2025-04-13 14:34:50,112 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='company.name' in template 488. Replace by @t-out 
2025-04-13 14:34:50,125 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,125 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,126 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,127 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,132 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,133 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,134 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,134 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,138 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,139 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,140 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,140 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,144 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,145 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,145 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,146 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,151 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,152 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,152 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,153 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,158 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_value' in template 490. Replace by @t-out 
2025-04-13 14:34:50,159 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc='kpi_subtitle' in template 490. Replace by @t-out 
2025-04-13 14:34:50,159 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,160 25504 WARNING thanh123 odoo.addons.base.models.ir_qweb: Found deprecated directive @t-esc="'%.2f' % kpi_margin" in template 490. Replace by @t-out 
2025-04-13 14:34:50,202 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Digest Emails` done. 
2025-04-13 14:34:50,202 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.299s (cron Digest Emails, server action 174 with uid 1) 
2025-04-13 14:34:50,205 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 14 updated and released 
2025-04-13 14:34:50,206 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 5 acquired 
2025-04-13 14:34:50,212 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 
2025-04-13 14:34:50,212 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     1, 
2025-04-13 14:34:50,212 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     '*', 
2025-04-13 14:34:50,212 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     'Notification: Delete Notifications older than 6 Month', 
2025-04-13 14:34:50,212 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron:                     128) 
2025-04-13 14:34:50,212 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-04-13 14:34:50,216 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Notification: Delete Notifications older than 6 Month` done. 
2025-04-13 14:34:50,216 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.004s (cron Notification: Delete Notifications older than 6 Month, server action 128 with uid 1) 
2025-04-13 14:34:50,217 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 5 updated and released 
2025-04-13 14:34:50,217 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 8 acquired 
2025-04-13 14:34:50,221 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Mail: send web push notification', 131) 
2025-04-13 14:34:50,222 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Mail: send web push notification`. 
2025-04-13 14:34:50,224 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Mail: send web push notification` done. 
2025-04-13 14:34:50,224 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.003s (cron Mail: send web push notification, server action 131 with uid 1) 
2025-04-13 14:34:50,226 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 8 updated and released 
2025-04-13 14:34:50,227 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 9 acquired 
2025-04-13 14:34:50,231 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Discuss: channel member unmute', 132) 
2025-04-13 14:34:50,231 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Discuss: channel member unmute`. 
2025-04-13 14:34:50,236 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Discuss: channel member unmute` done. 
2025-04-13 14:34:50,237 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.006s (cron Discuss: channel member unmute, server action 132 with uid 1) 
2025-04-13 14:34:50,238 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 9 updated and released 
2025-04-13 14:34:50,238 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 16 acquired 
2025-04-13 14:34:50,242 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Website Visitor : clean inactive visitors', 179) 
2025-04-13 14:34:50,243 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-04-13 14:34:50,247 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Website Visitor : clean inactive visitors` done. 
2025-04-13 14:34:50,247 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.004s (cron Website Visitor : clean inactive visitors, server action 179 with uid 1) 
2025-04-13 14:34:50,248 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 16 updated and released 
2025-04-13 14:34:50,249 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 10 acquired 
2025-04-13 14:34:50,253 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Users: Notify About Unregistered Users', 142) 
2025-04-13 14:34:50,253 25504 DEBUG ? odoo.service.server: cron1 polling for jobs 
2025-04-13 14:34:50,253 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-04-13 14:34:50,258 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Users: Notify About Unregistered Users` done. 
2025-04-13 14:34:50,258 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 10, skip 
2025-04-13 14:34:50,258 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.005s (cron Users: Notify About Unregistered Users, server action 142 with uid 1) 
2025-04-13 14:34:50,259 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 11 acquired 
2025-04-13 14:34:50,260 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 10 updated and released 
2025-04-13 14:34:50,262 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 11, skip 
2025-04-13 14:34:50,262 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 12 acquired 
2025-04-13 14:34:50,462 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'Partner Autocomplete: Sync with remote DB', 156) 
2025-04-13 14:34:50,463 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete: Sync with remote DB`. 
2025-04-13 14:34:50,467 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `Partner Autocomplete: Sync with remote DB` done. 
2025-04-13 14:34:50,467 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.003s (cron Partner Autocomplete: Sync with remote DB, server action 156 with uid 1) 
2025-04-13 14:34:50,469 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 11 updated and released 
2025-04-13 14:34:50,470 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: another worker is processing job 12, skip 
2025-04-13 14:34:50,473 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: cron.object.execute('thanh123', 1, '*', 'SMS: SMS Queue Manager', 160) 
2025-04-13 14:34:50,473 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-04-13 14:34:50,482 25504 INFO thanh123 odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-04-13 14:34:50,482 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: 0.010s (cron SMS: SMS Queue Manager, server action 160 with uid 1) 
2025-04-13 14:34:50,484 25504 DEBUG thanh123 odoo.addons.base.models.ir_cron: job 12 updated and released 
