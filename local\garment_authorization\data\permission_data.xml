<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Groups -->
    <record id="group_sample_viewer" model="res.groups">
        <field name="name">Garment Sample Viewer</field>
    </record>
    <record id="group_sample_approval" model="res.groups">
        <field name="name">Garment Sample Approval</field>
    </record>
    <record id="group_sample_management" model="res.groups">
        <field name="name">Garment Sample Management</field>
    </record>

    <record id="group_order_viewer" model="res.groups">
        <field name="name">Garment Order Viewer</field>
    </record>
    <record id="group_order_approval" model="res.groups">
        <field name="name">Garment Order Approval</field>
    </record>
    <record id="group_order_management" model="res.groups">
        <field name="name">Garment Order Management</field>
    </record>

    <record id="group_production_viewer" model="res.groups">
        <field name="name">Garment Production Viewer</field>
    </record>
    <record id="group_production_approval" model="res.groups">
        <field name="name">Garment Production Approval</field>
    </record>
    <record id="group_production_management" model="res.groups">
        <field name="name">Garment Production Management</field>
    </record>
    <record id="group_production_progress" model="res.groups">
        <field name="name">Garment Production Progress</field>
    </record>
    <record id="group_production_order" model="res.groups">
        <field name="name">Garment Production Order</field>
    </record>
    <record id="group_production_unit_price_process" model="res.groups">
        <field name="name">Garment Production Unit Price Process</field>
    </record>
    <record id="group_production_support_staff_count" model="res.groups">
        <field name="name">Garment Production Support Staff Count</field>
    </record>
    <record id="group_production_material_consumption" model="res.groups">
        <field name="name">Garment Production Finalize Material Consumption</field>
    </record>

    <record id="group_inventory_viewer" model="res.groups">
        <field name="name">Garment Inventory Viewer</field>
    </record>
    <record id="group_inventory_approval" model="res.groups">
        <field name="name">Garment Inventory Approval</field>
    </record>
    <record id="group_inventory_management" model="res.groups">
        <field name="name">Garment Inventory Management</field>
    </record>
    <record id="group_inventory_purchaser" model="res.groups">
        <field name="name">Garment Inventory Purchaser</field>
    </record>

    <record id="group_authorization_manager" model="res.groups">
        <field name="name">Garment Authorization Manager</field>
    </record>

    <!-- Permissions -->
    <record id="permission_sample_viewer" model="garment.permission">
        <field name="name">Sample Viewer</field>
        <field name="group_id" ref="group_sample_viewer"/>
    </record>
    <record id="permission_sample_approval" model="garment.permission">
        <field name="name">Sample Approval</field>
        <field name="group_id" ref="group_sample_approval"/>
    </record>
    <record id="permission_sample_management" model="garment.permission">
        <field name="name">Sample Management</field>
        <field name="group_id" ref="group_sample_management"/>
    </record>

    <record id="permission_order_viewer" model="garment.permission">
        <field name="name">Order Viewer</field>
        <field name="group_id" ref="group_order_viewer"/>
    </record>
    <record id="permission_order_approval" model="garment.permission">
        <field name="name">Order Approval</field>
        <field name="group_id" ref="group_order_approval"/>
    </record>
    <record id="permission_order_management" model="garment.permission">
        <field name="name">Order Management</field>
        <field name="group_id" ref="group_order_management"/>
    </record>

    <record id="permission_production_viewer" model="garment.permission">
        <field name="name">Production Viewer</field>
        <field name="group_id" ref="group_production_viewer"/>
    </record>
    <record id="permission_production_approval" model="garment.permission">
        <field name="name">Production Approval</field>
        <field name="group_id" ref="group_production_approval"/>
    </record>
    <record id="permission_production_management" model="garment.permission">
        <field name="name">Production Management</field>
        <field name="group_id" ref="group_production_management"/>
    </record>
    <record id="permission_production_progress" model="garment.permission">
        <field name="name">Production Progress</field>
        <field name="group_id" ref="group_production_progress"/>
    </record>
    <record id="permission_production_order" model="garment.permission">
        <field name="name">Production Order</field>
        <field name="group_id" ref="group_production_order"/>
    </record>
    <record id="permission_production_unit_price_process" model="garment.permission">
        <field name="name">Production Unit Price Process</field>
        <field name="group_id" ref="group_production_unit_price_process"/>
    </record>
    <record id="permission_production_support_staff_count" model="garment.permission">
        <field name="name">Production Support Staff Count</field>
        <field name="group_id" ref="group_production_support_staff_count"/>
    </record>
    <record id="permission_production_material_consumption" model="garment.permission">
        <field name="name">Production Finalize Material Consumption</field>
        <field name="group_id" ref="group_production_material_consumption"/>
    </record>

    <record id="permission_inventory_viewer" model="garment.permission">
        <field name="name">Inventory Viewer</field>
        <field name="group_id" ref="group_inventory_viewer"/>
    </record>
    <record id="permission_inventory_approval" model="garment.permission">
        <field name="name">Inventory Approval</field>
        <field name="group_id" ref="group_inventory_approval"/>
    </record>
    <record id="permission_inventory_management" model="garment.permission">
        <field name="name">Inventory Management</field>
        <field name="group_id" ref="group_inventory_management"/>
    </record>
    <record id="permission_inventory_purchaser" model="garment.permission">
        <field name="name">Inventory Purchaser</field>
        <field name="group_id" ref="group_inventory_purchaser"/>
    </record>

    <record id="permission_authorization_manager" model="garment.permission">
        <field name="name">Authorization Manager</field>
        <field name="group_id" ref="group_authorization_manager"/>
    </record>
</odoo>