<odoo>
    <record id="view_operation_price_tree" model="ir.ui.view">
        <field name="name">operation.price.tree</field>
        <field name="model">operation.price</field>
        <field name="arch" type="xml">
            <tree string="Operation Prices">
                <field name="operation_name"/>
                <field name="unit_price"/>
                <field name="description"/>
            </tree>
        </field>
    </record>

    <record id="view_operation_price_form" model="ir.ui.view">
        <field name="name">operation.price.form</field>
        <field name="model">operation.price</field>
        <field name="arch" type="xml">
            <form string="Operation Price">
                <sheet>
                    <group>
                        <field name="operation_name"/>
                        <field name="unit_price"/>
                        <field name="description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_operation_price" model="ir.actions.act_window">
        <field name="name">Operation Prices</field>
        <field name="res_model">operation.price</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- <menuitem id="menu_operation_price" name="Operation Prices"
              parent="menu_garment_production_root" action="action_operation_price" sequence="40"/> -->
</odoo>