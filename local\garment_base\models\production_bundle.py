from odoo import models, fields, api

class ProductionBundle(models.Model):
    _name = 'production.bundle'
    _description = 'Production Bundle'

    bundle_no = fields.Char('Bundle Number', required=True, unique=True)
    size = fields.Char('Size', required=True)
    qty = fields.Integer('Quantity', required=True)
    ticket_printed = fields.<PERSON><PERSON><PERSON>('Ticket Printed', default=False)
    is_completed = fields.<PERSON><PERSON>an('Completed', default=False, help="Check this when the bundle is completed")
    completion_date = fields.Datetime('Completion Date', readonly=True)
    
    # Relationships
    order_line_id = fields.Many2one('production.order.line', string='Order Line', required=True, ondelete='cascade')
    order_id = fields.Many2one('production.order', related='order_line_id.order_id', string='Production Order', store=True, readonly=True)
    
    # Related fields for display
    sample_name = fields.Char(related='order_id.sample_name', string='Sample Name', readonly=True)
    client = fields.Char(related='order_id.client', string='Client', readonly=True)
    
    # ADD THESE METHODS:
    def action_view_qr_ticket(self):
        """View QR ticket in browser"""
        self.ensure_one()
        return {
            'type': 'ir.actions.report',
            'report_name': 'garment_production.report_bundle_qr',
            'report_type': 'qweb-html',
            'data': {},
            'context': self.env.context,
        }

    def action_download_qr_ticket(self):
        """Download QR ticket as PDF"""
        self.ensure_one()
        return {
            'type': 'ir.actions.report',
            'report_name': 'garment_production.report_bundle_qr',
            'report_type': 'qweb-pdf',
            'data': {},
            'context': self.env.context,
        }

    @api.onchange('is_completed')
    def _onchange_is_completed(self):
        """Update completion date when bundle is marked as completed"""
        if self.is_completed:
            self.completion_date = fields.Datetime.now()
        else:
            self.completion_date = False
    
    def name_get(self):
        result = []
        for bundle in self:
            name = f"{bundle.bundle_no} - {bundle.size} ({bundle.qty} pcs)"
            if bundle.is_completed:
                name += " ✓"
            result.append((bundle.id, name))
        return result
    
    def toggle_completion(self):
        """Toggle completion status of the bundle"""
        for bundle in self:
            bundle.is_completed = not bundle.is_completed
            if bundle.is_completed:
                bundle.completion_date = fields.Datetime.now()
            else:
                bundle.completion_date = False