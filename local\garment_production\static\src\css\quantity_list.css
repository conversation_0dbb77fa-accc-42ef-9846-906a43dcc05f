.quantity_list_field textarea {
    font-family: monospace !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    padding: 15px !important;
    border-radius: 5px !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
    min-height: 300px !important;
    white-space: pre !important;
}

.o_quantity_list_widget {
    max-width: 800px;
    margin: 0 auto;
}

.o_quantity_list_widget table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.o_quantity_list_widget table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
    font-size: 14px;
    padding: 10px;
}

.o_quantity_list_widget table td {
    vertical-align: middle;
    padding: 6px;
}

.o_quantity_list_widget input {
    width: 100%;
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.o_quantity_list_widget input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.o_quantity_list_widget .btn-danger {
    padding: 4px 8px;
}

.o_quantity_list_widget .fa-trash {
    font-size: 12px;
}

.o_form_view .oe_title h1 {
    font-size: 1.5em;
}

.o_form_label {
    font-weight: 600;
}

/* Improve tab navigation */
.nav-tabs .nav-link {
    padding: 10px 16px;
    font-weight: 600;
}

/* Make the alert box more readable */
.alert-info {
    background-color: #e8f4f8;
    border-color: #d6e9f0;
    padding: 15px;
}

/* Improve button appearance */
.btn-primary {
    padding: 8px 16px;
}

/* Space between elements */
.mb-3 {
    margin-bottom: 1rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

/* Improve tab display */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.nav-tabs .nav-item {
    margin-bottom: -1px;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* Improve overall form appearance */
.o_form_view .o_form_sheet_bg {
    background-color: #f8f9fa !important;
}

.o_form_view .o_form_sheet {
    border: 1px solid #dee2e6;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Better field labels */
.o_form_label {
    color: #212529;
    font-size: 14px;
    margin-bottom: 4px;
}

.size_quantity_table_container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.size_quantity_table_container table {
    width: 100%;
    border-collapse: collapse;
}

.size_quantity_table_container th {
    background-color: #f8f9fa;
    padding: 12px;
    font-weight: 600;
    text-align: center;
    border: 1px solid #dee2e6;
}

.size_quantity_table_container td {
    padding: 8px;
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.size_quantity_table_container .form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.size_quantity_table_container .input-group {
    display: flex;
    align-items: center;
}

.size_quantity_table_container .input-group .form-control {
    flex: 1;
    border-radius: 4px 0 0 4px;
}

.size_quantity_table_container .btn-remove-row {
    padding: 8px 12px;
    border-radius: 0 4px 4px 0;
    margin-left: -1px;
}

.size_quantity_table_container .btn-add-row {
    margin-top: 10px;
    padding: 8px 16px;
}

.size_quantity_table_container .fa-trash {
    font-size: 12px;
}

.size_quantity_table_container .add-row-container td {
    padding: 15px;
    background-color: #f8f9fa;
}

.o_quantity_list_editor textarea {
    font-family: monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    padding: 15px !important;
    border-radius: 5px !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
    min-height: 200px !important;
    width: 100% !important;
}

.quantity_list_table_display {
    margin-top: 1rem;
}

.quantity_list_table_display table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.quantity_list_table_display th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #495057;
    font-size: 14px;
    padding: 10px;
}

.quantity_list_table_display td {
    vertical-align: middle;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
} 