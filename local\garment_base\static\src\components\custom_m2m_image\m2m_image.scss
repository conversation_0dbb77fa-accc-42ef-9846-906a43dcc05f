// Attachment Icons (common for many2many_binary widget and chat thread and chat composer)
// ------------------------------------------------------------------
$o-attachment-image-size: 38px;
$o-attachment-margin: 5px;

.o_attachment {
    position: relative;
    width: 100%;
    padding: $o-attachment-margin;

    @include media-breakpoint-up(md) {
        width: 50%;
    }
    @include media-breakpoint-up(lg) {
        width: 25%;
    }
    // many2many_binary widget for send mail with attachment
    &.o_attachment_many2many {
        @include media-breakpoint-up(md) {
            width: 50%;
        }
        @include media-breakpoint-up(lg) {
            width: 62%;
        }
    }

    .o_attachment_wrap {
        overflow: hidden;
        position: relative;
        border-bottom: 1px solid rgba(black, 0.1);
        border-radius: 2px;
        padding: 4px 6px 0 4px;
        background-color: rgba($o-black, 0.05);

        .o_attachment_delete_cross {
            float: right;
            cursor: pointer;
        }
    }

    &.o_attachment_editable .o_attachment_wrap  {
        padding-right: 40px;
    }

    .o_image {
        width: $o-attachment-image-size;
        height: $o-attachment-image-size;
        image-orientation: from-image; // Only supported in Firefox
        &.o_hover {
            @include o-hover-opacity($default-opacity: 1, $hover-opacity: 0.7);
        }
    }

    .o_attachment_view {
        cursor: zoom-in;
    }

    .caption {
        @include o-text-overflow(block);

        a {
            @include o-hover-text-color($default-color: $o-main-text-color, $hover-color: $headings-color);
        }
    }

    .o_attachment_progress_bar {
        display: none;
    }

    .o_attachment_uploaded, .o_attachment_delete {
        @include o-position-absolute(0, 0, 0, $left: auto);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
    }

    .o_attachment_delete {
        background: desaturate(map-get($theme-colors, 'primary'), 50%);
        color: white;
        cursor: pointer;
        font-size: 20px;
        transform: translateX(100%);
        transition: all 0.3s ease 0s;

        &:hover {
            background: map-get($theme-colors, 'primary');
        }
    }

    &.o_attachment_uploading {
        .o_attachment_progress_bar {
            display: inline-block;
            margin: 0 0 0 8px;
            border-radius: 2px;
            vertical-align: bottom;

            > div {
                font-size: 11px;
                padding: 0 7px;
            }
        }

        .o_attachment_delete, .o_attachment_uploaded {
            display: none;
        }
    }

    &:hover .o_attachment_delete {
        transition: all 0.1s ease 0s;
        transform: translateX(0);
    }
}

.o_m2m_image_grid {
    width: 100%;
    padding: 10px;

    .o_image_grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 16px;
    }

    .o_image_item {
        position: relative;
    }

    .o_image_wrapper {
        position: relative;
        width: 100%;
        border-radius: 4px;
        overflow: hidden;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;

        &.o_dropzone {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;

            &:hover, &.o_dropzone_dragover {
                background-color: #e9ecef;
                border-color: #0d6efd;
            }

            .o_dropzone_content {
                text-align: center;
                color: #6c757d;
                padding: 20px;

                i {
                    font-size: 2rem;
                    margin-bottom: 10px;
                }

                .o_dropzone_text {
                    font-size: 0.875rem;
                    line-height: 1.4;
                }
            }

            input[type="file"] {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                opacity: 0;
                cursor: pointer;
            }
        }
    }

    .o_image_link {
        display: block;
        width: 100%;
        aspect-ratio: 1;
        overflow: hidden;

        &:hover .o_image_thumbnail {
            transform: scale(1.05);
        }
    }

    .o_image_thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
    }

    .o_image_info {
        padding: 8px;
        background: white;
        border-top: 1px solid #dee2e6;

        .o_image_name {
            display: block;
            color: #212529;
            text-decoration: none;
            font-size: 0.875rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &:hover {
                color: #0d6efd;
            }
        }
    }

    .o_image_delete {
        position: absolute;
        top: 8px;
        right: 8px;
        color: white;
        font-size: 1rem;
        padding: 6px;
        cursor: pointer;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        transition: background-color 0.2s ease;
        opacity: 0;
        transition: opacity 0.2s ease;

        &:hover {
            background: rgba(0, 0, 0, 0.8);
        }
    }

    .o_image_wrapper:hover .o_image_delete {
        opacity: 1;
    }

    .o_image_upload {
        margin-top: 16px;
        text-align: center;

        .btn {
            padding: 8px 16px;
            font-size: 14px;
            
            i {
                margin-right: 8px;
            }
        }
    }
}
