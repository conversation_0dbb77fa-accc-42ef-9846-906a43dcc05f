<odoo>
    <record id="view_group_form" model="ir.ui.view">
        <field name="name">garment.group.form</field>
        <field name="model">garment.group</field>
        <field name="arch" type="xml">
            <form string="Group">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="permission_ids" widget="many2many_checkboxes"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_group_tree" model="ir.ui.view">
        <field name="name">garment.group.tree</field>
        <field name="model">garment.group</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="action_groups" model="ir.actions.act_window">
        <field name="name">Groups</field>
        <field name="res_model">garment.group</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>