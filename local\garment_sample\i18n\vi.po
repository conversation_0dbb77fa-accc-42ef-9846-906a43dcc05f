# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* garment_sample
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e-20231119\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-24 00:01+0000\n"
"PO-Revision-Date: 2025-05-24 00:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid ""
"<i class=\"fa fa-download\"/>\n"
"                            Export"
msgstr ""
"<i class=\"fa fa-download\"/>\n"
"                            Xuất"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Actual"
msgstr "Thực Tế"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid ""
"Are you sure you want to delete this record? This action cannot be undone."
msgstr ""
"Bạn có chắc chắn muốn xóa bản ghi này? Hành động này không thể hoàn tác."

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
msgid "Attachments"
msgstr "Tệp Đính Kèm"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Basic Info"
msgstr "Thông Tin Cơ Bản"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Budget"
msgstr "Ngân Sách"

#. module: garment_sample
#: model_terms:ir.actions.act_window,help:garment_sample.action_garment_sample
msgid "Create a new Garment Sample"
msgstr "Tạo Mẫu May Mặc Mới"

#. module: garment_sample
#: model_terms:ir.actions.act_window,help:garment_sample.action_finished_product_size
msgid "Create your first finished product size"
msgstr "Tạo kích thước thành phẩm đầu tiên của bạn"

#. module: garment_sample
#: model_terms:ir.actions.act_window,help:garment_sample.action_material_detail
msgid "Create your first material detail"
msgstr "Tạo thông tin nguyên liệu đầu tiên của bạn"

#. module: garment_sample
#: model_terms:ir.actions.act_window,help:garment_sample.action_other_cost
msgid "Create your first other cost"
msgstr "Tạo chi phí khác đầu tiên của bạn"

#. module: garment_sample
#: model_terms:ir.actions.act_window,help:garment_sample.action_process_table
msgid "Create your first process table"
msgstr "Tạo bảng quy trình đầu tiên của bạn"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Delete"
msgstr "Xóa"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_tree
msgid "Department"
msgstr "Bộ Phận"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Details"
msgstr "Chi Tiết"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Edit"
msgstr "Chỉnh Sửa"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_garment_sample_edit
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
msgid "Edit Garment Sample"
msgstr "Chỉnh Sửa Mẫu May Mặc"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Export Excel"
msgstr "Xuất Excel"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Export PDF"
msgstr "Xuất PDF"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_finished_product_size_form
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Finished Product Size"
msgstr "Kích Thước Thành Phẩm"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_finished_product_size
msgid "Finished Product Sizes"
msgstr "Kích Thước Thành Phẩm"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Garment Sample"
msgstr "Mẫu May Mặc"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_tree
msgid "Garment Samples"
msgstr "Mẫu May Mặc"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Image Details"
msgstr "Chi Tiết Hình Ảnh"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Item"
msgstr "Mục"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Mark as Discontinued"
msgstr "Đánh Dấu Ngừng Sử Dụng"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Mark as Ready for Production"
msgstr "Đánh Dấu Sẵn Sàng Sản Xuất"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Material Cost (1)"
msgstr "Chi Phí Nguyên Liệu (1)"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
#: model_terms:ir.ui.view,arch_db:garment_sample.view_material_detail_form
msgid "Material Detail"
msgstr "Chi Tiết Nguyên Liệu"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_material_detail
msgid "Material Details"
msgstr "Chi Tiết Nguyên Liệu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Material Issuance"
msgstr "Xuất Kho Nguyên Liệu"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/header_button/create_button.xml:0
#, python-format
msgid "New"
msgstr "Mới"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
#: model_terms:ir.ui.view,arch_db:garment_sample.view_other_cost_form
msgid "Other Cost"
msgstr "Chi Phí Khác"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Other Cost (3)"
msgstr "Chi Phí Khác (3)"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_other_cost
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
msgid "Other Costs"
msgstr "Chi Phí Khác"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Process Cost"
msgstr "Chi Phí Quy Trình"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Process Cost (2)"
msgstr "Chi Phí Quy Trình (2)"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Process Requirements"
msgstr "Yêu Cầu Quy Trình"

#. module: garment_sample
#: model:ir.actions.act_window,name:garment_sample.action_process_table
#: model_terms:ir.ui.view,arch_db:garment_sample.view_process_table_form
msgid "Process Table"
msgstr "Bảng Quy Trình"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
msgid "Process table"
msgstr "Bảng quy trình"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Profit (4-1-2-3)"
msgstr "Lợi Nhuận (4-1-2-3)"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Quotation (4)"
msgstr "Báo Giá (4)"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Related Documents"
msgstr "Tài Liệu Liên Quan"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Remarks"
msgstr "Ghi Chú"

#. module: garment_sample
#: model:ir.ui.menu,name:garment_sample.menu_garment_sample_root
msgid "Sample"
msgstr "Mẫu"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
#, python-format
msgid "Sample Cost Summary"
msgstr "Tổng Hợp Chi Phí Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Sample Detail"
msgstr "Chi Tiết Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_edit
msgid "Sample Details"
msgstr "Chi Tiết Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_tree
msgid "Sample Name"
msgstr "Tên Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Sample Progress"
msgstr "Tiến Độ Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_form_view
msgid "Sample Quotation"
msgstr "Báo Giá Mẫu"

#. module: garment_sample
#: model_terms:ir.ui.view,arch_db:garment_sample.view_garment_sample_tree
msgid "Sample number"
msgstr "Số Mẫu"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Single Piece"
msgstr "Giá đơn vị sản phẩm"

#. module: garment_sample
#. odoo-javascript
#: code:addons/garment_sample/static/src/components/sample_cost_summary_table/sample_cost_summary_table.xml:0
#, python-format
msgid "Total Cost (1+2+3)"
msgstr "Tổng Chi Phí (1+2+3)"

#. module: garment_sample
#. odoo-python
#: code:addons/garment_sample/controllers/main.py:0
#, python-format
msgid "New development in %(date_filter)s"
msgstr "Phát triển mới trong %(date_filter)s, chờ phê duyệt"

#. module: garment_sample
#. odoo-python
#: code:addons/garment_sample/controllers/main.py:0
#, python-format
msgid "Production available in %(date_filter)s"
msgstr "Có thể sản xuất vào %(date_filter)s"

#. module: garment_sample
#. odoo-python
#: code:addons/garment_sample/controllers/main.py:0
#, python-format
msgid "Eliminated in %(date_filter)s"
msgstr "Đã Loại bỏ trong %(date_filter)s"