<odoo>
    <!-- List/Tree View -->
    <!-- Add this enhanced tree view -->
    <record id="view_production_order_tree_enhanced" model="ir.ui.view">
        <field name="name">production.order.tree.enhanced</field>
        <field name="model">production.order</field>
        <field name="arch" type="xml">
            <tree string="Production Orders" 
                  decoration-info="state == 'draft'" 
                  decoration-warning="state == 'in_progress'" 
                  decoration-success="state == 'done'"
                  decoration-bf="progress_percentage &gt;= 100"
                  class="o_production_tree">
                <field name="name" string="Order #"/>
                <field name="sample_name" string="Sample"/>
                <field name="client" string="Client"/>
                <field name="quantity" string="Total Quantity"/>
                <field name="progress_percentage" widget="progressbar" string="Progress"/>
                <field name="cost_total" widget="monetary" string="Total Cost"/>
                <field name="planned_date" string="Planned"/>
                <field name="state" widget="badge" 
                       decoration-info="state == 'draft'" 
                       decoration-warning="state == 'in_progress'" 
                       decoration-success="state == 'done'"/>
                <field name="finance_state" widget="badge" 
                        decoration-danger="finance_state == 'waiting'"
                        decoration-warning="finance_state == 'partial'" 
                        decoration-success="finance_state == 'paid'"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_production_order_form" model="ir.ui.view">
        <field name="name">production.order.form</field>
        <field name="model">production.order</field>
        <field name="arch" type="xml">
            <form string="Production Order">
                <header>
                    <button name="set_in_progress" string="Start Production" type="object" 
                            class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="set_done" string="Mark as Done" type="object" 
                            class="oe_highlight" invisible="state != 'in_progress'"/>
                    <button name="generate_order_lines_from_sample" string="Generate Size Lines from Sample" 
                            type="object" class="btn-secondary" 
                            invisible="not sample_id or state != 'draft'"/>
                    <button name="generate_bundles" string="Generate Bundles" 
                            type="object" class="btn-info" 
                            invisible="state != 'draft'"/>
                    <button name="action_refresh_quantity" string="Refresh Quantities" 
                            type="object" class="btn-warning" 
                            title="Manually refresh quantity calculations"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,done"/>
                </header>
                <sheet>
                    <!-- Main Information -->
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_lines" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="line_count" widget="statinfo" string="Lines"/>
                        </button>
                    </div>
                    
                    <field name="image" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="sample_name" readonly="1"/>
                        </h2>
                    </div>
                    
                    <!-- Order Section -->
                    <group>
                        <group name="order_info" string="Order Information">
                            <field name="garment_order_id" 
                                options="{'no_create': True}"  
                                placeholder="Select an existing order first"
                                required="1"/>
                            <field name="order_name" readonly="1"/>
                            <field name="order_number" readonly="1"/>
                            <field name="client" readonly="1"/>
                            <field name="delivery_date" readonly="1"/>
                            <field name="planned_date"/>
                            <field name="quantity" string="Total Quantity" readonly="1" 
                                   class="o_field_highlight"/>
                        </group>

                        <group name="sample_info" string="Sample Information">
                            <field name="sample_id" 
                                options="{'no_create': True, 'no_open': True}" 
                                widget="many2one"
                                placeholder="Select order first to see available samples"
                            
                                domain="[('id', 'in', available_sample_ids)]"/>
                            <field name="sample_name" readonly="1"/>
                            <field name="sample_code" readonly="1"/>
                            <field name="brand" readonly="1"/>
                            <field name="development_date" readonly="1"/>
                            <field name="designer" readonly="1"/>
                            <field name="available_sample_ids" invisible="1"/>
                        </group>
        
                        <!-- FLOATING RIGHT HINTS -->
                        <div class="sample-float-hints">
                            <!-- Step 2: hiện khi đã chọn order nhưng chưa chọn sample -->
                            <div invisible="not garment_order_id or sample_id" class="alert alert-info shadow-sm step-callout" role="alert">
                                <strong>Step 2:</strong> Now select a sample from the available options below.
                            </div>
                            <!-- ✓ Ready: hiện khi đã chọn cả order và sample -->
                            <div invisible="not garment_order_id or not sample_id" class="alert alert-success shadow-sm" role="alert">
                                <strong>✓ Ready:</strong> Order and sample selected. You can now proceed with production setup.
                            </div>
                        </div>
                    </group>
        
                    <group>
                        <group name="production_info" string="Production Details">
                            <field name="finance_state" widget="badge" 
                                   decoration-danger="finance_state == 'waiting'"
                                   decoration-warning="finance_state == 'partial'"
                                   decoration-success="finance_state == 'paid'"/>
                            <field name="cost_total" widget="monetary"/>
                        </group>
                    </group>
                    
                    <!-- Progress Bar -->
                    <div name="progress">
                        <label for="progress_percentage" string="Progress"/>
                        <field name="progress_percentage" widget="progressbar"/>
                    </div>
                    
                    <!-- Tabs for different sections -->
                    <notebook>
                        <!-- Replace the Order Lines page section around line 142: -->
                        <page string="Order Lines" name="order_lines">
                             <div class="alert alert-info mb-3" role="alert">
                                <p><strong>Instructions:</strong> Order lines show the quantities for each size. Progress is automatically calculated based on completed bundles.</p>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <button name="generate_order_lines_from_sample"
                                            string="Generate from Sample" 
                                            type="object"
                                            class="btn btn-primary"
                                            invisible="not sample_id"
                                            style="padding: 8px 16px; font-size: 14px;"/>
                                </div>
                            </div>
                        
                            <field name="line_ids">
                                <tree editable="bottom" class="o_order_lines_table" decoration-info="progress == 0" decoration-warning="progress &gt; 0 and progress &lt; 100" decoration-success="progress &gt;= 100">
                                    <field name="size" string="Size"/>
                                    <field name="color" string="Color"/>
                                    <field name="planned_qty" string="Planned"/>
                                    <field name="done_qty" string="Done" readonly="1"/>
                                    <field name="progress" string="Progress %" widget="progressbar" readonly="1"/>
                                    <field name="completion_status" string="Status" readonly="1"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Replace the entire Bundles page section: -->
                        <page string="Bundles">
                            <div class="alert alert-info mb-3" role="alert">
                                <p><strong>Instructions:</strong> Bundle management allows you to organize production quantities. Mark bundles as completed to track progress.</p>
                            </div>
                        
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <button name="generate_bundles"
                                            string="Create Bundles" 
                                            type="object"
                                            class="btn btn-primary"
                                            style="padding: 8px 16px; font-size: 14px;"/>
                                </div>
                            </div>
                        
                            <field name="line_ids">
                                <tree create="false" delete="false">
                                    <field name="size" string="Size"/>
                                    <field name="planned_qty" string="Planned Qty"/>
                                    <field name="done_qty" string="Done Qty"/>
                                    <field name="progress" string="Progress %" widget="progressbar"/>
                                </tree>
                            </field>
                            
                            <!-- Show all bundles across all order lines with completion checkbox -->
                            <div class="mt-4">
                                <h4>All Bundles</h4>
                                <field name="line_ids" mode="tree">
                                    <tree create="false" delete="false">
                                        <field name="bundle_ids" widget="one2many_list">
                                            <tree editable="bottom" decoration-success="is_completed" decoration-muted="not is_completed">
                                                <field name="bundle_no" string="Bundle #"/>
                                                <field name="size"/>
                                                <field name="qty" string="Quantity"/>
                                                <field name="is_completed" string="Completed" widget="boolean_toggle"/>
                                                <field name="completion_date" string="Completed On" readonly="1"/>
                                                <field name="ticket_printed" string="Ticket Printed"/>
                                            </tree>
                                        </field>
                                    </tree>
                                </field>
                            </div>
                        </page>

                        <page string="Material Details" name="materials">
                            <field name="material_ids">
                                <tree class="material-table">
                                    <field name="name"/>
                                    <field name="item_number"/>
                                    <field name="specification"/>
                                    <field name="unit"/>
                                    <field name="quantity"/>
                                    <field name="total_usage"/>
                                    <field name="unit_price"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Process Price List" name="processes">
                            <field name="process_ids">
                                <tree editable="bottom" class="process-table">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="unit_price"/>
                                    <field name="multiplier" class="multiplier-col"/>
                                    <field name="quantity"/>
                                    <field name="total_price"/>
                                    <field name="note"/>
                                </tree>
                            </field>
                        </page>
                        
                        <page string="Process Requirements" name="process_requirements">
                            <field name="process_requirements" placeholder="Enter process requirements here..."/>
                        </page>
                        
                        <page string="Other Costs" name="other_costs">
                            <div class="alert alert-info mb-3" role="alert">
                                <p><strong>Information:</strong> These costs are imported from the sample. They represent additional costs related to production.</p>
                            </div>
                            
                            <button name="action_remove_other_cost_entries" string="Clean Up Empty Entries" type="object" class="oe_highlight oe_right"/>
                            
                            <field name="cost_ids" domain="[('cost_type', '=', 'other')]" context="{'default_cost_type': 'other'}" options="{'always_reload': true}">
                                <tree editable="bottom">
                                    <field name="note" string="Cost Name"/>
                                    <field name="amount" widget="monetary" sum="Total"/>
                                    <field name="cost_type" invisible="1"/>
                                </tree>
                            </field>
                            
                            <group class="oe_subtotal_footer">
                                <field name="total_other_cost" string="Total Other Cost" class="oe_subtotal_footer_separator" readonly="1" widget="monetary"/>
                            </group>
                        </page>
                        
                        <page string="Costs" name="costs">
                            <div class="alert alert-info mb-3" role="alert">
                                <p><strong>Cost Summary:</strong> This shows the automatically calculated cost totals from different sections.</p>
                            </div>
                            
                            <group class="oe_cost_summary">
                                <group string="Cost Breakdown" class="oe_cost_table">
                                    <field name="total_other_cost" string="Total Other Cost" widget="monetary" readonly="1"/>
                                    <field name="total_material_cost" string="Material Cost" widget="monetary" readonly="1"/>
                                    <field name="total_process_cost" string="Process Cost" widget="monetary" readonly="1"/>
                                </group>
                            </group>
                            
                            <group class="oe_subtotal_footer">
                                <field name="cost_total" string="Total Cost" class="oe_subtotal_footer_separator" readonly="1" widget="monetary"/>
                            </group>
                        </page>
                        
                        <page string="Progress Entries" name="progress_entries">
                            <field name="progress_ids">
                                <tree editable="bottom">
                                    <field name="step_name"/>
                                    <field name="completed_qty"/>
                                    <field name="date"/>
                                    <field name="note"/>
                                </tree>
                            </field>
                        </page>
                        
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <!-- Keep the finance inheritance view but make sure it doesn't duplicate -->

    <record id="view_production_order_form_simple_finance" model="ir.ui.view">
        <field name="name">production.order.form.simple.finance</field>
        <field name="model">production.order</field>
        <field name="inherit_id" ref="view_production_order_form"/>
        <field name="arch" type="xml">
            <!-- Add finance buttons to header -->
            <header position="inside">
                <button name="action_finance_wizard" string="💰 Update Finance" 
                        type="object" class="btn-info"/>
                <button name="action_mark_fully_paid" string="✅ Mark Paid" 
                        type="object" class="btn-success"
                        invisible="finance_state == 'paid'"/>
            </header>
            
            <!-- Add payment info after finance_state -->
            <field name="finance_state" position="after">
                <field name="payment_amount" widget="monetary" 
                       invisible="finance_state == 'waiting' and payment_amount == 0"/>
                <field name="finance_approved_by" invisible="not finance_approved_by"/>
            </field>
            
            <!-- Add simple finance tab -->
            <notebook position="inside">
                <page string="Finance Info" name="finance_simple">
                    <group>
                        <group string="Payment Status">
                            <field name="finance_state" widget="badge" 
                                   decoration-danger="finance_state == 'waiting'"
                                   decoration-warning="finance_state == 'partial'"
                                   decoration-success="finance_state == 'paid'"/>
                            <field name="payment_amount" widget="monetary"/>
                            <field name="cost_total" widget="monetary" readonly="1"/>
                        </group>
                        <group string="Approval Info">
                            <field name="finance_approved_by" readonly="1"/>
                            <field name="finance_approval_date" readonly="1"/>
                        </group>
                    </group>
                    
                    <group string="Payment Notes">
                        <field name="payment_notes" nolabel="1" placeholder="Enter payment details, reference numbers, etc..."/>
                    </group>
                    
                    <div class="mt-3">
                        <button name="action_finance_wizard" string="Update Finance Status" 
                                type="object" class="btn btn-primary"/>
                        <button name="action_mark_fully_paid" string="Mark as Fully Paid" 
                                type="object" class="btn btn-success"
                                invisible="finance_state == 'paid'"/>
                    </div>
                </page>
            </notebook>
        </field>
    </record>
    
    <!-- Kanban View -->
       <record id="view_production_order_kanban_enhanced" model="ir.ui.view">
        <field name="name">production.order.kanban.enhanced</field>
        <field name="model">production.order</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_production_kanban">
                <field name="name"/>
                <field name="sample_name"/>
                <field name="client"/>
                <field name="quantity"/>
                <field name="progress_percentage"/>
                <field name="cost_total"/>
                <field name="state"/>
                <field name="planned_date"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click production_kanban_card">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="text-muted">
                                        <i class="fa fa-tag" title="Tag"/> <field name="sample_name"/>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" title="More options"/>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="o_kanban_card_content">
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Client:</strong><br/>
                                        <span class="text-muted"><field name="client"/></span>
                                    </div>
                                    <div class="col-6 text-end">
                                        <strong>Quantity:</strong><br/>
                                        <span class="badge badge-pill badge-info">
                                            <field name="quantity"/> pcs
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mt-2">
                                    <div class="mb-1">
                                        <strong>Progress: </strong>
                                        <span class="float-end">
                                            <field name="progress_percentage"/>%
                                        </span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" 
                                             t-att-style="'width: ' + record.progress_percentage.raw_value + '%'"
                                             t-att-class="record.progress_percentage.raw_value >= 100 ? 'bg-success' : (record.progress_percentage.raw_value >= 50 ? 'bg-warning' : 'bg-info')">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-2">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">
                                                <i class="fa fa-calendar" title="Calendar"/> 
                                                <field name="planned_date"/>
                                            </small>
                                        </div>
                                        <div class="col-6 text-end">
                                            <strong class="text-primary">
                                                <field name="cost_total" widget="monetary"/>
                                            </strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="o_kanban_card_manage_pane">
                                <div class="o_kanban_card_manage_section o_kanban_manage_reports">
                                    <div>
                                        <a type="edit">Edit</a>
                                    </div>
                                    <div>
                                        <a type="delete">Delete</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_production_order_search" model="ir.ui.view">
        <field name="name">production.order.search</field>
        <field name="model">production.order</field>
        <field name="arch" type="xml">
            <search string="Search Production Orders">
                <field name="name"/>
                <field name="client"/>
                <field name="department"/>
                <field name="style_id"/>
                <field name="sample_name"/>
                <field name="sample_code"/>
                <field name="brand"/>
                <field name="designer"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"/>
                <filter string="In Progress" name="in_progress" domain="[('state','=','in_progress')]"/>
                <filter string="Done" name="done" domain="[('state','=','done')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Client" name="groupby_client" context="{'group_by': 'client'}"/>
                    <filter string="Style" name="groupby_style" context="{'group_by': 'style_id'}"/>
                    <filter string="Sample Name" name="groupby_sample_name" context="{'group_by': 'sample_name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_production_orders" model="ir.actions.act_window">
        <field name="name">Production Orders</field>
        <field name="res_model">production.order</field>
        <field name="view_mode">tree,kanban,form,pivot,graph</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new production order
            </p>
        </field>
    </record>

    <!-- Root Menu -->
    <menuitem id="menu_garment_production_root" 
              name="Production"
              web_icon="garment_production,static/description/icon.png"
              sequence="10"/>
              
    <!-- Configuration Menu -->
    <menuitem id="menu_garment_production_config"
              name="Configuration"
              parent="menu_garment_production_root"
              sequence="100"/>

    <!-- Menu Item -->
    <menuitem id="menu_production_orders"
              name="Production Orders"
              parent="menu_garment_production_root"
              action="action_production_orders"
              sequence="10"/>
</odoo>
