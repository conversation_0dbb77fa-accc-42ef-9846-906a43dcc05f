<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="production_management.QuantityListTable">
        <t t-if="props.record.data[props.name]">
            <!-- Removed the controls div with 5 buttons -->
            
            <!-- Actual table -->
            <div class="table-responsive" style="width: 100%;">
                <table class="table table-sm table-bordered table-hover bold-first" style="width: 100%;">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" t-foreach="state.rows[0] || []" t-as="cell" t-key="cell_index">
                                <span t-esc="cell"/>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="state.rows.slice(1)" t-as="row" t-key="'row_' + row_index">
                            <td t-foreach="row" t-as="cell" t-key="cell_index">
                                <span 
                                    class="cell-text" 
                                    t-att-contenteditable="!props.readonly" 
                                    t-on-blur="(ev) => onCellBlur(ev, row_index + 1, cell_index)" 
                                    t-esc="cell"
                                />
                            </td>
                        </tr>
                    </tbody>
                    <tfoot t-if="state.rows.length > 1">
                        <tr class="table-info">
                            <th>Total</th>
                            <th><span t-esc="getTotalQuantity()"/></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </t>
    </t>
</templates> 